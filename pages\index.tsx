import { GetServerSideProps } from 'next';

// This page redirects to Order Fee as the new default
// Using server-side redirect to avoid client-side routing conflicts
const Index = () => {
    // This component should never render since we redirect server-side
    return null;
};

export const getServerSideProps: GetServerSideProps = async ({ query }) => {
    // Preserve all query parameters (especially important for BigCommerce context)
    const queryString = new URLSearchParams(query as Record<string, string>).toString();
    const destination = queryString ? `/order-fee?${queryString}` : '/order-fee';

    return {
        redirect: {
            destination,
            permanent: false, // Use 302 redirect (temporary) since this is a routing preference
        },
    };
};

export default Index;
