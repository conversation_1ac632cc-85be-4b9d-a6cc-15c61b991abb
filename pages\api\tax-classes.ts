
import { NextApiRequest, NextApiResponse } from 'next';
import { getSession } from '../../lib/auth';

export default async function taxClasses(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    res.status(405).json({ error: 'Method not allowed' });
    return;
  }

  try {
    const { storeHash, accessToken } = await getSession(req);
    
    // Create BigCommerce API client
    const bigCommerceUrl = `https://api.bigcommerce.com/stores/${storeHash}/v2/tax_classes`;
    
    const response = await fetch(bigCommerceUrl, {
      headers: {
        'X-Auth-Token': accessToken || '',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`BigCommerce API error: ${response.status} ${response.statusText}`);
    }

    const taxClasses = await response.json();

    res.status(200).json({
      success: true,
      data: taxClasses
    });
  } catch (error) {
    console.error('Error fetching tax classes:', error);
    res.status(500).json({ 
      error: 'Failed to fetch tax classes',
      message: error.message 
    });
  }
}

