import { NextApiRequest, NextApiResponse } from 'next';
import { getSession } from '../../../lib/auth';
import { fullFeeRuleSynchronization } from '../../../lib/bidirectional-sync';
import { performDatabaseMaintenance, emergencyCheckoutCleanup } from '../../../lib/database-cleanup';

/**
 * Webhook management endpoint for administrative operations
 * Provides tools for managing the webhook-based fee synchronization system
 */
export default async function webhookManage(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== 'POST') {
        res.setHeader('Allow', ['POST']);
        res.status(405).json({ error: `Method ${req.method} Not Allowed` });
        return;
    }

    try {
        const { storeHash } = await getSession(req);
        const { action, parameters = {} } = req.body;

        if (!action) {
            res.status(400).json({
                error: 'Missing required field: action',
                supportedActions: [
                    'full_sync',
                    'emergency_cleanup',
                    'database_maintenance',
                    'force_recalculation'
                ]
            });
            return;
        }

        let result;

        switch (action) {
            case 'full_sync':
                console.log(`Starting full fee rule synchronization for store ${storeHash}`);
                result = await fullFeeRuleSynchronization(storeHash);
                break;

            case 'emergency_cleanup':
                const { checkoutId } = parameters;
                if (!checkoutId) {
                    res.status(400).json({
                        error: 'Missing required parameter: checkoutId for emergency_cleanup'
                    });
                    return;
                }
                console.log(`Starting emergency cleanup for checkout ${checkoutId} in store ${storeHash}`);
                result = await emergencyCheckoutCleanup(storeHash, checkoutId);
                break;

            case 'database_maintenance':
                console.log(`Starting database maintenance for store ${storeHash}`);
                result = await performDatabaseMaintenance(storeHash, parameters);
                break;

            case 'force_recalculation':
                // This would force recalculation of all fees on all active checkouts
                console.log(`Starting forced fee recalculation for store ${storeHash}`);
                result = await fullFeeRuleSynchronization(storeHash);
                result.action = 'force_recalculation';
                break;

            default:
                res.status(400).json({
                    error: `Unsupported action: ${action}`,
                    supportedActions: [
                        'full_sync',
                        'emergency_cleanup', 
                        'database_maintenance',
                        'force_recalculation'
                    ]
                });
                return;
        }

        res.status(200).json({
            success: true,
            action,
            timestamp: Date.now(),
            storeHash,
            parameters,
            result
        });

    } catch (error) {
        console.error(`Error in webhook management action:`, error);
        res.status(500).json({
            success: false,
            error: 'Management action failed',
            message: error.message,
            timestamp: Date.now()
        });
    }
}

/**
 * Configuration endpoint for webhook system settings
 */
export async function webhookConfig(req: NextApiRequest, res: NextApiResponse) {
    if (req.method === 'GET') {
        return handleGetConfig(req, res);
    } else if (req.method === 'PUT') {
        return handleUpdateConfig(req, res);
    } else {
        res.setHeader('Allow', ['GET', 'PUT']);
        res.status(405).json({ error: `Method ${req.method} Not Allowed` });
        return;
    }
}

async function handleGetConfig(req: NextApiRequest, res: NextApiResponse) {
    try {
        const { storeHash } = await getSession(req);

        // Return current webhook system configuration
        const config = {
            webhookEndpoints: {
                checkout: '/api/webhooks/checkout',
                health: '/api/webhooks/health',
                manage: '/api/webhooks/manage'
            },
            supportedEvents: [
                'store/checkout/created',
                'store/checkout/updated',
                'store/checkout/deleted'
            ],
            features: {
                bidirectionalSync: true,
                automaticCleanup: true,
                realTimeSync: true,
                batchOperations: true
            },
            settings: {
                maxCheckoutAge: 24, // hours
                cleanupInterval: 'manual', // could be 'hourly', 'daily', etc.
                syncRetries: 3,
                syncTimeout: 30000 // milliseconds
            }
        };

        res.status(200).json({
            success: true,
            storeHash,
            config,
            timestamp: Date.now()
        });

    } catch (error) {
        console.error('Error getting webhook config:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to get configuration',
            message: error.message
        });
    }
}

async function handleUpdateConfig(req: NextApiRequest, res: NextApiResponse) {
    try {
        const { storeHash } = await getSession(req);
        const { settings } = req.body;

        if (!settings) {
            res.status(400).json({
                error: 'Missing required field: settings'
            });
            return;
        }

        // In a real implementation, you would save these settings to the database
        // For now, we'll just validate and return them
        const validatedSettings = {
            maxCheckoutAge: settings.maxCheckoutAge || 24,
            cleanupInterval: settings.cleanupInterval || 'manual',
            syncRetries: settings.syncRetries || 3,
            syncTimeout: settings.syncTimeout || 30000
        };

        res.status(200).json({
            success: true,
            message: 'Configuration updated successfully',
            storeHash,
            settings: validatedSettings,
            timestamp: Date.now()
        });

    } catch (error) {
        console.error('Error updating webhook config:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to update configuration',
            message: error.message
        });
    }
}
