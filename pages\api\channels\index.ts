import { NextApiRequest, NextApiResponse } from 'next';
import { bigcommerceClient, getSession } from '../../../lib/auth';

export interface Channel {
    id: number;
    name: string;
}

export default async function channels(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== 'GET') {
        res.setHeader('Allow', ['GET']);
        res.status(405).json({ error: `Method ${req.method} Not Allowed` });
        return;
    }

    try {
        const { storeHash, accessToken } = await getSession(req);
        const bigcommerce = bigcommerceClient(accessToken, storeHash);

        console.log(`Fetching channels for store ${storeHash}`);

        // Fetch channels from BigCommerce API
        const response = await bigcommerce.get('/channels');
        console.log("channels", response.data);
        
        if (!response.data) {
            throw new Error('No data received from BigCommerce channels API');
        }

        // Transform the response to include only id and name fields
        const channels: Channel[] = response.data.map((channel: any) => ({
            id: channel.id,
            name: channel.name
        }));

        console.log(`Successfully fetched ${channels.length} channels`);

        res.status(200).json({
            success: true,
            data: channels,
            count: channels.length
        });

    } catch (error) {
        console.error('Error fetching channels:', error);
        
        // Handle specific BigCommerce API errors
        if (error.response) {
            const { status, data } = error.response;
            console.error(`BigCommerce API error ${status}:`, data);
            
            // Handle common error scenarios
            if (status === 403) {
                res.status(200).json({
                    success: false,
                    error: 'Insufficient permissions to access channels data',
                    fallback: true,
                    data: []
                });
                return;
            }
            
            if (status === 404) {
                res.status(200).json({
                    success: true,
                    data: [],
                    count: 0,
                    message: 'No channels found'
                });
                return;
            }
        }

        // Provide fallback response for any other errors
        res.status(200).json({
            success: false,
            error: error.message || 'Failed to fetch channels',
            fallback: true,
            data: []
        });
    }
}