import { NextApiRequest, NextApiResponse } from 'next';
import { getSession } from '../../../lib/auth';
import { getAllActiveCheckouts } from '../../../lib/checkout-management';
import { getActiveFeeRules } from '../../../lib/fee-application';
import { performDatabaseMaintenance } from '../../../lib/database-cleanup';

/**
 * Webhook system health monitoring endpoint
 * Provides status information about the webhook-based fee synchronization system
 */
export default async function webhookHealth(req: NextApiRequest, res: NextApiResponse) {
    if (req.method === 'GET') {
        return handleHealthCheck(req, res);
    } else if (req.method === 'POST') {
        return handleMaintenanceOperation(req, res);
    } else {
        res.setHeader('Allow', ['GET', 'POST']);
        res.status(405).json({ error: `Method ${req.method} Not Allowed` });
        return;
    }
}

async function handleHealthCheck(req: NextApiRequest, res: NextApiResponse) {
    try {
        const { storeHash } = await getSession(req);

        // Get system status information
        const [activeCheckouts, activeFeeRules] = await Promise.all([
            getAllActiveCheckouts(storeHash),
            getActiveFeeRules(storeHash)
        ]);

        // Calculate some basic statistics
        const totalAppliedFees = activeCheckouts.reduce(
            (total, checkout) => total + (checkout.appliedFees?.length || 0), 
            0
        );

        const checkoutsByStatus = activeCheckouts.reduce((acc, checkout) => {
            acc[checkout.status] = (acc[checkout.status] || 0) + 1;
            return acc;
        }, {} as Record<string, number>);

        const feeRulesByStatus = activeFeeRules.reduce((acc, rule) => {
            const status = rule.active ? 'active' : 'inactive';
            acc[status] = (acc[status] || 0) + 1;
            return acc;
        }, {} as Record<string, number>);

        // System health indicators
        const healthIndicators = {
            webhookSystemActive: true, // This would be determined by actual webhook registration status
            databaseConnected: true,   // This would be determined by database connectivity
            syncSystemOperational: activeCheckouts.length >= 0 && activeFeeRules.length >= 0
        };

        const overallHealth = Object.values(healthIndicators).every(indicator => indicator);

        res.status(200).json({
            success: true,
            timestamp: Date.now(),
            storeHash,
            health: {
                overall: overallHealth ? 'healthy' : 'degraded',
                indicators: healthIndicators
            },
            statistics: {
                activeCheckouts: activeCheckouts.length,
                activeFeeRules: activeFeeRules.length,
                totalAppliedFees,
                checkoutsByStatus,
                feeRulesByStatus
            },
            system: {
                webhookEndpoints: [
                    '/api/webhooks/checkout',
                ],
                supportedEvents: [
                    'store/checkout/created',
                    'store/checkout/updated', 
                    'store/checkout/deleted'
                ],
                bidirectionalSync: true,
                databaseCleanup: true
            }
        });

    } catch (error) {
        console.error('Error in webhook health check:', error);
        res.status(500).json({
            success: false,
            error: 'Health check failed',
            message: error.message,
            timestamp: Date.now()
        });
    }
}

async function handleMaintenanceOperation(req: NextApiRequest, res: NextApiResponse) {
    try {
        const { storeHash } = await getSession(req);
        const { operation, options = {} } = req.body;

        if (!operation) {
            res.status(400).json({
                error: 'Missing required field: operation'
            });
            return;
        }

        let result;

        switch (operation) {
            case 'database_maintenance':
                console.log(`Starting database maintenance for store ${storeHash}`);
                result = await performDatabaseMaintenance(storeHash, options);
                break;

            case 'health_check':
                // Redirect to GET handler
                return handleHealthCheck(req, res);

            default:
                res.status(400).json({
                    error: `Unsupported operation: ${operation}`,
                    supportedOperations: ['database_maintenance', 'health_check']
                });
                return;
        }

        res.status(200).json({
            success: true,
            operation,
            timestamp: Date.now(),
            storeHash,
            result
        });

    } catch (error) {
        console.error('Error in webhook maintenance operation:', error);
        res.status(500).json({
            success: false,
            error: 'Maintenance operation failed',
            message: error.message,
            timestamp: Date.now()
        });
    }
}
