import { NextApiRequest, NextApiResponse } from 'next';
import { getSession } from '../../../lib/auth';
import { validateFeeRuleName } from '../../../lib/validation';

export default async function validateName(req: NextApiRequest, res: NextApiResponse) {
    if (req.method === 'POST') {
        return handleValidateName(req, res);
    } else {
        res.setHeader('Allow', ['POST']);
        res.status(405).json({ error: `Method ${req.method} Not Allowed` });
        return;
    }
}

async function handleValidateName(req: NextApiRequest, res: NextApiResponse) {
    try {
        // Get the session to extract store information
        const { storeHash } = await getSession(req);

        const { name, excludeFeeRuleId } = req.body;

        // Validate required fields
        if (!name || typeof name !== 'string') {
            res.status(400).json({ 
                error: 'Name is required and must be a string' 
            });
            return;
        }

        // Validate the name
        const validationResult = await validateFeeRuleName(storeHash, name, excludeFeeRuleId);

        res.status(200).json({
            success: true,
            isValid: validationResult.isValid,
            error: validationResult.error || null,
            storeHash,
        });

    } catch (error) {
        console.error('Error validating fee rule name:', error);
        res.status(500).json({
            error: 'Internal server error',
            message: error.message
        });
    }
}
