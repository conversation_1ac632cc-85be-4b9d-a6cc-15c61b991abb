/**
 * Comprehensive error handling and logging utilities for webhook processing
 * and fee synchronization operations
 */

export interface RetryOptions {
    maxRetries: number;
    baseDelay: number; // milliseconds
    maxDelay: number; // milliseconds
    backoffMultiplier: number;
}

export interface ErrorContext {
    operation: string;
    storeHash?: string;
    checkoutId?: string;
    feeRuleId?: string;
    timestamp: number;
    metadata?: Record<string, any>;
}

export interface OperationResult<T = any> {
    success: boolean;
    data?: T;
    error?: string;
    retryCount?: number;
    duration?: number;
    context?: ErrorContext;
}

/**
 * Default retry configuration for different operation types
 */
export const DEFAULT_RETRY_OPTIONS: Record<string, RetryOptions> = {
    webhook: {
        maxRetries: 3,
        baseDelay: 1000,
        maxDelay: 10000,
        backoffMultiplier: 2
    },
    feeSync: {
        maxRetries: 2,
        baseDelay: 2000,
        maxDelay: 8000,
        backoffMultiplier: 1.5
    },
    apiCall: {
        maxRetries: 3,
        baseDelay: 500,
        maxDelay: 5000,
        backoffMultiplier: 2
    },
    database: {
        maxRetries: 2,
        baseDelay: 1000,
        maxDelay: 4000,
        backoffMultiplier: 2
    }
};

/**
 * Enhanced logging utility with structured logging
 */
export class Logger {
    private context: string;

    constructor(context: string) {
        this.context = context;
    }

    private formatMessage(level: string, message: string, metadata?: any): string {
        const timestamp = new Date().toISOString();
        const baseLog = `[${timestamp}] [${level}] [${this.context}] ${message}`;
        
        if (metadata) {
            return `${baseLog} | ${JSON.stringify(metadata)}`;
        }
        return baseLog;
    }

    info(message: string, metadata?: any): void {
        console.log(this.formatMessage('INFO', message, metadata));
    }

    warn(message: string, metadata?: any): void {
        console.warn(this.formatMessage('WARN', message, metadata));
    }

    error(message: string, error?: any, metadata?: any): void {
        const errorData = error ? {
            message: error.message,
            stack: error.stack,
            status: error.status,
            ...metadata
        } : metadata;
        
        console.error(this.formatMessage('ERROR', message, errorData));
    }

    debug(message: string, metadata?: any): void {
        if (process.env.NODE_ENV === 'development') {
            console.debug(this.formatMessage('DEBUG', message, metadata));
        }
    }
}

/**
 * Retry mechanism with exponential backoff
 */
export async function withRetry<T>(
    operation: () => Promise<T>,
    context: ErrorContext,
    options: RetryOptions = DEFAULT_RETRY_OPTIONS.apiCall
): Promise<OperationResult<T>> {
    const logger = new Logger('RetryHandler');
    const startTime = Date.now();
    let lastError: any;

    for (let attempt = 0; attempt <= options.maxRetries; attempt++) {
        try {
            logger.debug(`Attempting operation: ${context.operation}`, {
                attempt: attempt + 1,
                maxRetries: options.maxRetries + 1,
                context
            });

            const result = await operation();
            const duration = Date.now() - startTime;

            logger.info(`Operation succeeded: ${context.operation}`, {
                attempt: attempt + 1,
                duration,
                context
            });

            return {
                success: true,
                data: result,
                retryCount: attempt,
                duration,
                context
            };

        } catch (error) {
            lastError = error;
            const isLastAttempt = attempt === options.maxRetries;

            logger.warn(`Operation failed: ${context.operation}`, {
                attempt: attempt + 1,
                maxRetries: options.maxRetries + 1,
                error: error.message,
                isLastAttempt,
                context
            });

            if (isLastAttempt) {
                break;
            }

            // Calculate delay with exponential backoff
            const delay = Math.min(
                options.baseDelay * Math.pow(options.backoffMultiplier, attempt),
                options.maxDelay
            );

            logger.debug(`Retrying operation after delay: ${context.operation}`, {
                delay,
                nextAttempt: attempt + 2,
                context
            });

            await new Promise(resolve => setTimeout(resolve, delay));
        }
    }

    const duration = Date.now() - startTime;
    
    logger.error(`Operation failed after all retries: ${context.operation}`, lastError, {
        totalAttempts: options.maxRetries + 1,
        duration,
        context
    });

    return {
        success: false,
        error: lastError?.message || 'Unknown error',
        retryCount: options.maxRetries,
        duration,
        context
    };
}

/**
 * Safe execution wrapper that catches and logs errors
 */
export async function safeExecute<T>(
    operation: () => Promise<T>,
    context: ErrorContext,
    fallbackValue?: T
): Promise<OperationResult<T>> {
    const logger = new Logger('SafeExecute');
    const startTime = Date.now();

    try {
        logger.debug(`Starting safe execution: ${context.operation}`, { context });
        
        const result = await operation();
        const duration = Date.now() - startTime;

        logger.info(`Safe execution completed: ${context.operation}`, {
            duration,
            context
        });

        return {
            success: true,
            data: result,
            duration,
            context
        };

    } catch (error) {
        const duration = Date.now() - startTime;
        
        logger.error(`Safe execution failed: ${context.operation}`, error, {
            duration,
            context,
            hasFallback: fallbackValue !== undefined
        });

        return {
            success: false,
            data: fallbackValue,
            error: error.message || 'Unknown error',
            duration,
            context
        };
    }
}

/**
 * Webhook-specific error handler
 */
export function handleWebhookError(error: any, context: ErrorContext): OperationResult {
    const logger = new Logger('WebhookErrorHandler');

    // Categorize webhook errors
    if (error.status === 401 || error.status === 403) {
        logger.error('Webhook authentication error', error, context);
        return {
            success: false,
            error: 'Webhook authentication failed',
            context
        };
    }

    if (error.status === 400) {
        logger.error('Webhook validation error', error, context);
        return {
            success: false,
            error: 'Invalid webhook payload',
            context
        };
    }

    if (error.status >= 500) {
        logger.error('Webhook server error', error, context);
        return {
            success: false,
            error: 'Server error processing webhook',
            context
        };
    }

    logger.error('Unknown webhook error', error, context);
    return {
        success: false,
        error: error.message || 'Unknown webhook error',
        context
    };
}

/**
 * BigCommerce API specific error handler
 */
export function handleBigCommerceError(error: any, context: ErrorContext): OperationResult {
    const logger = new Logger('BigCommerceErrorHandler');

    // Handle specific BigCommerce API errors
    if (error.status === 404) {
        logger.warn('BigCommerce resource not found', { error: error.message, context });
        return {
            success: false,
            error: 'Resource not found (checkout or fee may have been deleted)',
            context
        };
    }

    if (error.status === 422) {
        logger.warn('BigCommerce validation error', { error: error.message, context });
        return {
            success: false,
            error: 'Validation error (fee may be invalid or already processed)',
            context
        };
    }

    if (error.status === 429) {
        logger.warn('BigCommerce rate limit exceeded', { error: error.message, context });
        return {
            success: false,
            error: 'Rate limit exceeded - operation should be retried',
            context
        };
    }

    logger.error('BigCommerce API error', error, context);
    return {
        success: false,
        error: error.message || 'BigCommerce API error',
        context
    };
}

/**
 * Create error context for operations
 */
export function createErrorContext(
    operation: string,
    storeHash?: string,
    checkoutId?: string,
    feeRuleId?: string,
    metadata?: Record<string, any>
): ErrorContext {
    return {
        operation,
        storeHash,
        checkoutId,
        feeRuleId,
        timestamp: Date.now(),
        metadata
    };
}
