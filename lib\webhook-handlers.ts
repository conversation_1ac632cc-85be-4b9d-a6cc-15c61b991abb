import { CheckoutWebhookPayload } from '../types/bigcommerce';
import { applyFeesToCheckout, getActiveFeeRules } from './fee-application';
import { withRetry, safeExecute, createErrorContext, Logger, DEFAULT_RETRY_OPTIONS } from './error-handling';
import db from './db';

// Implement cleanup functions directly to avoid circular imports
async function cleanupDeletedCheckout(storeHash: string, checkoutId: string) {
    const logger = new Logger('CheckoutCleanup');
    logger.info(`Cleaning up deleted checkout ${checkoutId} in store ${storeHash}`);

    try {
        let cleanedRecords = 0;
        const details = [];

        // Remove the checkout from active checkouts
        const checkoutRemoved = await db.removeActiveCheckout(storeHash, checkoutId);
        if (checkoutRemoved) {
            cleanedRecords++;
            details.push('Removed active checkout record');
        }

        // Clean up appliedFeeHistory records that reference this checkout
        const historyCleanupResult = await db.cleanupAppliedFeeHistoryForCheckout(storeHash, checkoutId);
        cleanedRecords += historyCleanupResult.cleanedRecords;
        details.push(`Cleaned up ${historyCleanupResult.cleanedRecords} appliedFeeHistory records`);

        // Remove from legacy checkoutIds collection if it exists
        const legacyRemoved = await db.removeLegacyCheckoutId(storeHash, checkoutId);
        if (legacyRemoved) {
            cleanedRecords++;
            details.push('Removed legacy checkout ID record');
        }

        logger.info(`Successfully cleaned up checkout ${checkoutId}: ${cleanedRecords} records removed`);

        return {
            success: true,
            cleanedRecords,
            details
        };

    } catch (error) {
        logger.error(`Error cleaning up deleted checkout ${checkoutId}`, error);
        throw error;
    }
}

async function recalculateCheckoutFees(storeHash: string, checkoutId: string) {
    const logger = new Logger('FeeRecalculation');
    logger.info(`Recalculating fees for checkout ${checkoutId} in store ${storeHash}`);

    // For webhook handlers, we'll implement a simplified version
    // The full implementation would be in checkout-management.ts
    try {
        // Get existing checkout data
        const existingCheckout = await db.getActiveCheckout(storeHash, checkoutId);

        if (!existingCheckout) {
            throw new Error(`Checkout ${checkoutId} not found in database`);
        }

        // Apply all active fee rules to the checkout
        const feeApplicationResult = await applyFeesToCheckout(
            storeHash,
            checkoutId,
            existingCheckout.customer,
            existingCheckout.channel
        );

        // Extract applied fee IDs
        const appliedFeeIds = feeApplicationResult.appliedFees.map(fee => fee.id);

        logger.info(`Successfully recalculated fees for checkout ${checkoutId}: ${appliedFeeIds.length} fees applied`);

        return {
            success: feeApplicationResult.success,
            appliedFeesCount: feeApplicationResult.appliedFees.length,
            appliedFeeIds,
            errorsCount: feeApplicationResult.errors.length
        };

    } catch (error) {
        logger.error(`Error recalculating fees for checkout ${checkoutId}`, error);
        throw error;
    }
}

/**
 * Handle checkout created webhook
 * When a new checkout is created, apply all active fee rules to it
 * 
 * @param storeHash - Store hash for database operations
 * @param checkoutId - ID of the created checkout
 * @param webhookPayload - Full webhook payload for additional context
 * @returns Promise<any> - Result of fee application
 */
export async function handleCheckoutCreated(
    storeHash: string,
    checkoutId: string,
    webhookPayload: CheckoutWebhookPayload
): Promise<any> {
    const logger = new Logger('CheckoutCreatedHandler');
    const context = createErrorContext('checkout_created', storeHash, checkoutId);

    logger.info(`Handling checkout created: ${checkoutId} for store ${storeHash}`, {
        webhookScope: webhookPayload.scope,
        storeId: webhookPayload.store_id
    });

    // Use retry mechanism for the entire operation
    const result = await withRetry(async () => {
        // Store the new checkout in our database with retry
        const storeResult = await safeExecute(
            () => db.storeActiveCheckout({
                checkoutId,
                storeHash,
                status: 'active',
                createdAt: Date.now(),
                updatedAt: Date.now(),
                appliedFees: []
            }),
            createErrorContext('store_active_checkout', storeHash, checkoutId)
        );

        if (!storeResult.success) {
            throw new Error(`Failed to store checkout: ${storeResult.error}`);
        }

        // Get all active fee rules for this store with retry
        const feeRulesResult = await safeExecute(
            () => getActiveFeeRules(storeHash),
            createErrorContext('get_active_fee_rules', storeHash)
        );

        if (!feeRulesResult.success) {
            throw new Error(`Failed to get fee rules: ${feeRulesResult.error}`);
        }

        const activeFeeRules = feeRulesResult.data || [];

        if (activeFeeRules.length === 0) {
            logger.info(`No active fee rules found for store ${storeHash}`);
            return {
                success: true,
                message: 'No active fee rules to apply',
                appliedFeesCount: 0
            };
        }

        logger.info(`Applying ${activeFeeRules.length} fee rules to checkout ${checkoutId}`);

        // Apply all active fee rules to the new checkout with retry
        const feeApplicationResult = await safeExecute(
            () => applyFeesToCheckout(
                storeHash,
                checkoutId,
                null, // customer data not available from webhook
                null  // channel data not available from webhook
            ),
            createErrorContext('apply_fees_to_checkout', storeHash, checkoutId)
        );

        if (!feeApplicationResult.success) {
            throw new Error(`Failed to apply fees: ${feeApplicationResult.error}`);
        }

        const feeResult = feeApplicationResult.data;

        // Update checkout record with applied fee IDs
        if (feeResult.success && feeResult.appliedFees.length > 0) {
            const appliedFeeIds = feeResult.appliedFees.map(fee => fee.id);

            const updateResult = await safeExecute(
                () => db.updateActiveCheckout(storeHash, checkoutId, {
                    appliedFees: appliedFeeIds,
                    updatedAt: Date.now()
                }),
                createErrorContext('update_active_checkout', storeHash, checkoutId)
            );

            if (!updateResult.success) {
                logger.warn(`Failed to update checkout record: ${updateResult.error}`);
            }
        }

        logger.info(`Successfully processed checkout created for ${checkoutId}: ${feeResult.appliedFees.length} fees applied`);

        return {
            success: feeResult.success,
            appliedFeesCount: feeResult.appliedFees.length,
            errorsCount: feeResult.errors.length,
            appliedFees: feeResult.appliedFees.map(fee => ({
                id: fee.id,
                name: fee.name,
                cost: fee.cost || fee.cost_inc_tax || fee.cost_ex_tax || 0
            })),
            errors: feeResult.errors.map(error => ({
                feeRuleName: error.feeRule?.name || 'Unknown',
                error: error.error
            }))
        };

    }, context, DEFAULT_RETRY_OPTIONS.webhook);

    if (!result.success) {
        logger.error(`Failed to handle checkout created after retries: ${checkoutId}`, new Error(result.error), {
            retryCount: result.retryCount,
            duration: result.duration
        });
        throw new Error(result.error);
    }

    return result.data;
}

/**
 * Handle checkout updated webhook
 * When a checkout is updated, recalculate and reapply all fees
 * 
 * @param storeHash - Store hash for database operations
 * @param checkoutId - ID of the updated checkout
 * @param webhookPayload - Full webhook payload for additional context
 * @returns Promise<any> - Result of fee recalculation
 */
export async function handleCheckoutUpdated(
    storeHash: string, 
    checkoutId: string, 
    webhookPayload: CheckoutWebhookPayload
): Promise<any> {
    console.log(`Handling checkout updated: ${checkoutId} for store ${storeHash}`);

    try {
        // Check if checkout exists in our database
        const existingCheckout = await db.getActiveCheckout(storeHash, checkoutId);
        
        if (!existingCheckout) {
            // If checkout doesn't exist, treat as new checkout
            console.log(`Checkout ${checkoutId} not found in database, treating as new checkout`);
            return await handleCheckoutCreated(storeHash, checkoutId, webhookPayload);
        }

        // Recalculate and reapply all fees for the updated checkout
        const recalculationResult = await recalculateCheckoutFees(storeHash, checkoutId);

        // Update checkout record
        await db.updateActiveCheckout(storeHash, checkoutId, {
            updatedAt: Date.now(),
            appliedFees: recalculationResult.appliedFeeIds || existingCheckout.appliedFees
        });

        console.log(`Successfully processed checkout updated for ${checkoutId}: ${recalculationResult.appliedFeesCount} fees applied`);

        return recalculationResult;

    } catch (error) {
        console.error(`Error handling checkout updated for ${checkoutId}:`, error);
        throw error;
    }
}

/**
 * Handle checkout deleted webhook
 * When a checkout is deleted, clean up all related data
 * 
 * @param storeHash - Store hash for database operations
 * @param checkoutId - ID of the deleted checkout
 * @param webhookPayload - Full webhook payload for additional context
 * @returns Promise<any> - Result of cleanup operation
 */
export async function handleCheckoutDeleted(
    storeHash: string,
    checkoutId: string,
    webhookPayload: CheckoutWebhookPayload
): Promise<any> {
    const logger = new Logger('CheckoutDeletedHandler');
    const context = createErrorContext('checkout_deleted', storeHash, checkoutId);

    logger.info(`Handling checkout deleted: ${checkoutId} for store ${storeHash}`, {
        webhookScope: webhookPayload.scope,
        storeId: webhookPayload.store_id
    });

    // Use retry mechanism for the cleanup operation
    const result = await withRetry(async () => {
        // Clean up all data related to the deleted checkout with error handling
        const cleanupResult = await safeExecute(
            () => cleanupDeletedCheckout(storeHash, checkoutId),
            createErrorContext('cleanup_deleted_checkout', storeHash, checkoutId)
        );

        if (!cleanupResult.success) {
            throw new Error(`Failed to cleanup checkout: ${cleanupResult.error}`);
        }

        logger.info(`Successfully processed checkout deleted for ${checkoutId}: cleaned up ${cleanupResult.data.cleanedRecords} records`);

        return {
            success: true,
            message: 'Checkout cleanup completed',
            cleanedRecords: cleanupResult.data.cleanedRecords,
            details: cleanupResult.data.details
        };

    }, context, DEFAULT_RETRY_OPTIONS.database);

    if (!result.success) {
        logger.error(`Failed to handle checkout deleted after retries: ${checkoutId}`, new Error(result.error), {
            retryCount: result.retryCount,
            duration: result.duration
        });
        throw new Error(result.error);
    }

    return result.data;
}
