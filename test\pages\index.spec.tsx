import Index, { getServerSideProps } from '@pages/index';
import { render } from '@test/utils';

describe('Homepage Redirect', () => {
    test('renders null component (should not be visible due to server-side redirect)', () => {
        const { container } = render(<Index />);

        // Component should render null since redirect happens server-side
        expect(container.firstChild).toBeNull();
    });

    test('getServerSideProps redirects to order-fee without query params', async () => {
        const context = {
            query: {},
            req: {},
            res: {},
            resolvedUrl: '/',
        };

        const result = await getServerSideProps(context as any);

        expect(result).toEqual({
            redirect: {
                destination: '/order-fee',
                permanent: false,
            },
        });
    });

    test('getServerSideProps preserves query parameters during redirect', async () => {
        const context = {
            query: { context: 'test-context', other: 'param' },
            req: {},
            res: {},
            resolvedUrl: '/?context=test-context&other=param',
        };

        const result = await getServerSideProps(context as any);

        expect(result).toEqual({
            redirect: {
                destination: '/order-fee?context=test-context&other=param',
                permanent: false,
            },
        });
    });
});
