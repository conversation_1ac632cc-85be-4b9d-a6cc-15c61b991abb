export interface CheckoutData {
    id: string;
    cart: {
        base_amount: number;
        discount_amount: number;
        cart_amount_inc_tax: number; // Cart subtotal including tax - used for percentage fee calculations
        line_items?: {
            physical_items?: Array<{
                product_id: number;
                quantity: number;
                list_price: number;
                sale_price: number;
                name: string;
            }>;
            digital_items?: Array<{
                product_id: number;
                quantity: number;
                list_price: number;
                sale_price: number;
                name: string;
            }>;
        };
    };
    grand_total: number;
    taxes: Array<{
        name: string;
        amount: number;
    }>;
    coupons: Array<{
        id: string;
        code: string;
        amount: number;
    }>;
    fees?: FeeResponse[];
}

export interface ProductData {
    id: number;
    name: string;
    price: number;
    sale_price?: number;
    brand_id: number;
    categories: number[];
}

// Webhook-related types
export interface WebhookPayload {
    scope: string;
    store_id: string;
    data: {
        type: string;
        id: number | string;
    };
    hash: string;
    created_at: number;
    producer: string;
}

export interface CheckoutWebhookPayload extends WebhookPayload {
    scope: 'store/checkout/created' | 'store/checkout/updated' | 'store/checkout/deleted';
    data: {
        type: 'checkout';
        id: string; // Checkout ID
    };
}

export interface WebhookVerificationData {
    payload: string;
    signature: string;
    timestamp?: number;
}

export interface ActiveCheckoutData {
    checkoutId: string;
    storeHash: string;
    status: 'active' | 'completed' | 'abandoned' | 'deleted';
    createdAt: number;
    updatedAt: number;
    customer?: any;
    channel?: any;
    appliedFees?: string[]; // Array of BigCommerce fee IDs applied to this checkout
}

export interface FeeRequest {
    id?: string; // Optional - required for updates, not for creation
    type: 'custom_fee';
    name: string;
    display_name: string;
    cost: number;
    source?: string;
    tax_class_id?: number;
}

export interface FeeRequestBody {
    fees: FeeRequest[];
}

export interface FeeDeletionRequestBody {
    ids: string[]; // Array of fee IDs to delete
}

export interface FeeResponse {
    id: string;
    type: string;
    name: string;
    display_name: string;
    cost_ex_tax: number;
    cost_inc_tax: number;
    source?: string;
    tax_class_id?: number;
    // Legacy cost property for backward compatibility
    cost?: number;
}

export interface BigCommerceApiError {
    status: number;
    title: string;
    detail?: string;
    errors?: Record<string, string>;
}
