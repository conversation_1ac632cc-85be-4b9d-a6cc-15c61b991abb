import { ActiveCheckoutData } from '../types/bigcommerce';
import { applyFeesToCheckout, getActiveFeeRules } from './fee-application';
import { deleteCheckoutFees } from './bigcommerce-api';
import db from './db';

/**
 * Recalculate and reapply all fees for a specific checkout
 * This removes existing fees and applies fresh fees based on current fee rules
 * 
 * @param storeHash - Store hash for database operations
 * @param checkoutId - ID of the checkout to recalculate fees for
 * @returns Promise<any> - Result of fee recalculation
 */
export async function recalculateCheckoutFees(storeHash: string, checkoutId: string): Promise<any> {
    console.log(`Recalculating fees for checkout ${checkoutId} in store ${storeHash}`);

    try {
        // Get existing checkout data
        const existingCheckout = await db.getActiveCheckout(storeHash, checkoutId);
        
        if (!existingCheckout) {
            throw new Error(`Checkout ${checkoutId} not found in database`);
        }

        // Remove existing fees from the checkout
        if (existingCheckout.appliedFees && existingCheckout.appliedFees.length > 0) {
            console.log(`Removing ${existingCheckout.appliedFees.length} existing fees from checkout ${checkoutId}`);
            
            const accessToken = await db.getStoreToken(storeHash);
            if (!accessToken) {
                throw new Error(`No access token found for store ${storeHash}`);
            }

            await deleteCheckoutFees(storeHash, accessToken, checkoutId, existingCheckout.appliedFees);
        }

        // Get all active fee rules
        const activeFeeRules = await getActiveFeeRules(storeHash);
        
        if (activeFeeRules.length === 0) {
            console.log(`No active fee rules found for store ${storeHash}`);
            return {
                success: true,
                message: 'No active fee rules to apply',
                appliedFeesCount: 0,
                appliedFeeIds: []
            };
        }

        // Apply all active fee rules to the checkout
        const feeApplicationResult = await applyFeesToCheckout(
            storeHash,
            checkoutId,
            existingCheckout.customer,
            existingCheckout.channel
        );

        // Extract applied fee IDs
        const appliedFeeIds = feeApplicationResult.appliedFees.map(fee => fee.id);

        console.log(`Successfully recalculated fees for checkout ${checkoutId}: ${appliedFeeIds.length} fees applied`);

        return {
            success: feeApplicationResult.success,
            appliedFeesCount: feeApplicationResult.appliedFees.length,
            appliedFeeIds,
            errorsCount: feeApplicationResult.errors.length,
            appliedFees: feeApplicationResult.appliedFees.map(fee => ({
                id: fee.id,
                name: fee.name,
                cost: fee.cost || fee.cost_inc_tax || fee.cost_ex_tax || 0
            })),
            errors: feeApplicationResult.errors.map(error => ({
                feeRuleName: error.feeRule?.name || 'Unknown',
                error: error.error
            }))
        };

    } catch (error) {
        console.error(`Error recalculating fees for checkout ${checkoutId}:`, error);
        throw error;
    }
}

/**
 * Clean up all data related to a deleted checkout
 * This includes removing the checkout record and cleaning up appliedFeeHistory
 * 
 * @param storeHash - Store hash for database operations
 * @param checkoutId - ID of the deleted checkout
 * @returns Promise<any> - Result of cleanup operation
 */
export async function cleanupDeletedCheckout(storeHash: string, checkoutId: string): Promise<any> {
    console.log(`Cleaning up deleted checkout ${checkoutId} in store ${storeHash}`);

    try {
        let cleanedRecords = 0;
        const details = [];

        // Remove the checkout from active checkouts
        const checkoutRemoved = await db.removeActiveCheckout(storeHash, checkoutId);
        if (checkoutRemoved) {
            cleanedRecords++;
            details.push('Removed active checkout record');
        }

        // Clean up appliedFeeHistory records that reference this checkout
        const historyCleanupResult = await db.cleanupAppliedFeeHistoryForCheckout(storeHash, checkoutId);
        cleanedRecords += historyCleanupResult.cleanedRecords;
        details.push(`Cleaned up ${historyCleanupResult.cleanedRecords} appliedFeeHistory records`);

        // Remove from legacy checkoutIds collection if it exists
        const legacyRemoved = await db.removeLegacyCheckoutId(storeHash, checkoutId);
        if (legacyRemoved) {
            cleanedRecords++;
            details.push('Removed legacy checkout ID record');
        }

        console.log(`Successfully cleaned up checkout ${checkoutId}: ${cleanedRecords} records removed`);

        return {
            success: true,
            cleanedRecords,
            details
        };

    } catch (error) {
        console.error(`Error cleaning up deleted checkout ${checkoutId}:`, error);
        throw error;
    }
}

/**
 * Get all active checkouts for a store
 * Used for applying fee rule changes to all active checkouts
 * 
 * @param storeHash - Store hash for database operations
 * @returns Promise<ActiveCheckoutData[]> - Array of active checkouts
 */
export async function getAllActiveCheckouts(storeHash: string): Promise<ActiveCheckoutData[]> {
    try {
        return await db.getActiveCheckouts(storeHash);
    } catch (error) {
        console.error(`Error getting active checkouts for store ${storeHash}:`, error);
        throw error;
    }
}

/**
 * Apply fee rule changes to all active checkouts
 * Used when fee rules are created, updated, or deleted
 * 
 * @param storeHash - Store hash for database operations
 * @param operation - Type of operation: 'create', 'update', 'delete'
 * @param feeRuleId - ID of the fee rule that changed (optional for create)
 * @returns Promise<any> - Result of applying changes to all checkouts
 */
export async function applyFeeRuleChangesToAllCheckouts(
    storeHash: string, 
    operation: 'create' | 'update' | 'delete',
    feeRuleId?: string
): Promise<any> {
    console.log(`Applying fee rule ${operation} to all active checkouts in store ${storeHash}`);

    try {
        // Get all active checkouts
        const activeCheckouts = await getAllActiveCheckouts(storeHash);
        
        if (activeCheckouts.length === 0) {
            console.log(`No active checkouts found for store ${storeHash}`);
            return {
                success: true,
                message: 'No active checkouts to update',
                processedCheckouts: 0
            };
        }

        console.log(`Found ${activeCheckouts.length} active checkouts to update`);

        const results = [];
        let successCount = 0;
        let errorCount = 0;

        // Process each checkout
        for (const checkout of activeCheckouts) {
            try {
                const result = await recalculateCheckoutFees(storeHash, checkout.checkoutId);
                
                // Update checkout record with new applied fees
                await db.updateActiveCheckout(storeHash, checkout.checkoutId, {
                    appliedFees: result.appliedFeeIds || [],
                    updatedAt: Date.now()
                });

                results.push({
                    checkoutId: checkout.checkoutId,
                    success: true,
                    appliedFeesCount: result.appliedFeesCount
                });
                successCount++;

            } catch (error) {
                console.error(`Error processing checkout ${checkout.checkoutId}:`, error);
                results.push({
                    checkoutId: checkout.checkoutId,
                    success: false,
                    error: error.message
                });
                errorCount++;
            }
        }

        console.log(`Completed fee rule ${operation} application: ${successCount} successful, ${errorCount} errors`);

        return {
            success: errorCount === 0,
            processedCheckouts: activeCheckouts.length,
            successCount,
            errorCount,
            results
        };

    } catch (error) {
        console.error(`Error applying fee rule ${operation} to all checkouts:`, error);
        throw error;
    }
}
