import { NextApiRequest, NextApiResponse } from 'next';
import { getSession } from '../../lib/auth';
import db from '../../lib/db';
import { FeeRuleData } from '../../types/db';
import { synchronizeFeeRuleAcrossCheckouts } from '../../lib/bidirectional-sync';

export default async function feeRules(req: NextApiRequest, res: NextApiResponse) {
    if (req.method === 'POST') {
        return handleCreateFeeRule(req, res);
    } else if (req.method === 'GET') {
        return handleGetFeeRules(req, res);
    } else if (req.method === 'PUT') {
        return handleUpdateFeeRule(req, res);
    } else if (req.method === 'DELETE') {
        return handleDeleteFeeRule(req, res);
    } else {
        res.setHeader('Allow', ['GET', 'POST', 'PUT', 'DELETE']);
        res.status(405).json({ error: `Method ${req.method} Not Allowed` });
        return;
    }
}

async function handleCreateFeeRule(req: NextApiRequest, res: NextApiResponse) {
    try {
        // Get the session to extract store information
        const { storeHash } = await getSession(req);

        const { name, type, display_name, cost, source, tax_class_id, active, selectedBrands, selectedCategories, selectedCustomerGroups, selectedChannels, selectedProducts } = req.body;

        // Validate required fields
        if (!name || !type || !display_name || cost === undefined || !source || active === undefined) {
            res.status(400).json({
                error: 'Missing required fields: name, type, display_name, cost, source, and active are required'
            });
            return;
        }

        // Validate source field
        if (!source.trim()) {
            res.status(400).json({
                error: 'Source field cannot be empty - required by BigCommerce API'
            });
            return;
        }

        // Validate type
        if (!['percentage', 'fixed'].includes(type)) {
            res.status(400).json({
                error: 'Type must be either "percentage" or "fixed"'
            });
            return;
        }

        // Validate cost
        if (typeof cost !== 'number' || cost < 0) {
            res.status(400).json({
                error: 'Cost must be a non-negative number'
            });
            return;
        }

        // Validate active
        if (typeof active !== 'boolean') {
            res.status(400).json({
                error: 'Active must be a boolean'
            });
            return;
        }

        const feeRuleData: FeeRuleData = {
            name: name.trim(),
            type,
            display_name: display_name.trim(),
            cost,
            source: source.trim(), // Now required field
            active,
            created_at: Date.now(),
            storeHash,
        };

        // Only add optional fields if they have valid values
        if (tax_class_id !== undefined && tax_class_id !== null && typeof tax_class_id === 'number') {
            feeRuleData.tax_class_id = tax_class_id;
        }

        // Add filter fields if they exist (including empty arrays)
        if (selectedBrands !== undefined && Array.isArray(selectedBrands)) {
            feeRuleData.selectedBrands = selectedBrands;
        }

        if (selectedCategories !== undefined && Array.isArray(selectedCategories)) {
            feeRuleData.selectedCategories = selectedCategories;
        }

        if (selectedCustomerGroups !== undefined && Array.isArray(selectedCustomerGroups)) {
            feeRuleData.selectedCustomerGroups = selectedCustomerGroups;
        }

        // Add channels filter field
        if (selectedChannels !== undefined && Array.isArray(selectedChannels)) {
            feeRuleData.selectedChannels = selectedChannels;
        }

        // Add products filter field
        if (selectedProducts !== undefined && Array.isArray(selectedProducts)) {
            feeRuleData.selectedProducts = selectedProducts;
        }

        // Store the fee rule in the database
        const feeRuleId = await db.storeFeeRule(feeRuleData);

        // Prepare the response data
        const responseData = {
            id: feeRuleId,
            ...feeRuleData
        };

        // Automatic fee synchronization across all active checkouts (if fee rule is active)
        let syncResult: any = null;
        try {
            // Only attempt synchronization if the new fee rule is active
            if (feeRuleData.active) {
                console.log(`New active fee rule "${feeRuleData.name}" created. Attempting synchronization across all active checkouts...`);

                // Apply the fee rule to all active checkouts using bidirectional sync
                const feeRuleWithId = { ...feeRuleData, id: feeRuleId };
                syncResult = await synchronizeFeeRuleAcrossCheckouts(storeHash, feeRuleWithId, 'create');

                console.log(`Fee rule synchronization completed:`, {
                    success: syncResult.success,
                    processedCheckouts: syncResult.processedCheckouts,
                    successCount: syncResult.successCount,
                    errorCount: syncResult.errorCount
                });
            } else {
                console.log(`Fee rule "${feeRuleData.name}" is inactive. Skipping synchronization.`);
            }
        } catch (error) {
            console.error('Error during fee rule synchronization:', error);
            // Don't fail the fee rule creation if synchronization fails
            syncResult = {
                success: false,
                processedCheckouts: 0,
                successCount: 0,
                errorCount: 1,
                error: error.message || 'Unknown synchronization error occurred'
            };
        }

        res.status(201).json({
            success: true,
            message: 'Fee rule created successfully',
            data: responseData,
            synchronization: syncResult ? {
                attempted: true,
                success: syncResult.success,
                processedCheckouts: syncResult.processedCheckouts,
                successCount: syncResult.successCount,
                errorCount: syncResult.errorCount,
                operation: 'create',
                details: syncResult.results || [],
                error: syncResult.error
            } : {
                attempted: false,
                reason: feeRuleData.active ? 'No active checkouts available' : 'Fee rule is inactive'
            }
        });

    } catch (error) {
        console.error('Error creating fee rule:', error);
        res.status(500).json({ 
            error: 'Internal server error',
            message: error.message 
        });
    }
}

async function handleGetFeeRules(req: NextApiRequest, res: NextApiResponse) {
    try {
        // Get the session to extract store information
        const { storeHash } = await getSession(req);

        // Retrieve fee rules from the database
        const feeRules = await db.getFeeRules(storeHash);

        res.status(200).json({
            success: true,
            data: feeRules,
            count: feeRules.length,
            storeHash,
        });

    } catch (error) {
        console.error('Error retrieving fee rules:', error);
        res.status(500).json({
            error: 'Internal server error',
            message: error.message
        });
    }
}

async function handleUpdateFeeRule(req: NextApiRequest, res: NextApiResponse) {
    try {
        // Get the session to extract store information
        const { storeHash } = await getSession(req);

        const { feeRuleId, name, type, display_name, cost, source, tax_class_id, active, selectedBrands, selectedCategories, selectedCustomerGroups, selectedChannels, selectedProducts } = req.body;

        // Validate required fields
        if (!feeRuleId) {
            res.status(400).json({
                error: 'Missing required field: feeRuleId is required'
            });
            return;
        }

        if (!name || !type || !display_name || cost === undefined || !source || active === undefined) {
            res.status(400).json({
                error: 'Missing required fields: name, type, display_name, cost, source, and active are required'
            });
            return;
        }

        // Validate source field
        if (!source.trim()) {
            res.status(400).json({
                error: 'Source field cannot be empty - required by BigCommerce API'
            });
            return;
        }

        // Validate type
        if (!['percentage', 'fixed'].includes(type)) {
            res.status(400).json({
                error: 'Invalid type: must be either "percentage" or "fixed"'
            });
            return;
        }

        // Validate cost
        if (typeof cost !== 'number' || cost < 0) {
            res.status(400).json({
                error: 'Invalid cost: must be a non-negative number'
            });
            return;
        }

        // Validate tax_class_id if provided
        if (tax_class_id !== undefined && (typeof tax_class_id !== 'number' || tax_class_id < 0)) {
            res.status(400).json({
                error: 'Invalid tax_class_id: must be a non-negative number'
            });
            return;
        }

        // Validate active
        if (typeof active !== 'boolean') {
            res.status(400).json({
                error: 'Invalid active: must be a boolean'
            });
            return;
        }

        const updateData: Partial<FeeRuleData> = {
            name: name.trim(),
            type,
            display_name: display_name.trim(),
            cost,
            source: source.trim(), // Now required field
            active,
        };

        // Only add optional fields if they have valid values
        if (tax_class_id !== undefined && tax_class_id !== null && typeof tax_class_id === 'number') {
            updateData.tax_class_id = tax_class_id;
        }

        // Add filter fields if they exist (including empty arrays)
        if (selectedBrands !== undefined && Array.isArray(selectedBrands)) {
            updateData.selectedBrands = selectedBrands;
        }

        if (selectedCategories !== undefined && Array.isArray(selectedCategories)) {
            updateData.selectedCategories = selectedCategories;
        }

        if (selectedCustomerGroups !== undefined && Array.isArray(selectedCustomerGroups)) {
            updateData.selectedCustomerGroups = selectedCustomerGroups;
        }

        // Add channels filter field
        if (selectedChannels !== undefined && Array.isArray(selectedChannels)) {
            updateData.selectedChannels = selectedChannels;
        }

        // Add products filter field
        if (selectedProducts !== undefined && Array.isArray(selectedProducts)) {
            updateData.selectedProducts = selectedProducts;
        }

        // Get the original fee rule before updating for synchronization
        const originalFeeRule = await db.getFeeRule(storeHash, feeRuleId);
        if (!originalFeeRule) {
            res.status(404).json({
                error: 'Fee rule not found'
            });
            return;
        }

        // Update the fee rule in the database
        await db.updateFeeRule(storeHash, feeRuleId, updateData);

        // Get the updated fee rule to return
        const updatedFeeRule = await db.getFeeRule(storeHash, feeRuleId);

        // Perform bidirectional synchronization for fee rule updates
        let synchronizationResult = null;
        const originalActive = originalFeeRule.active;
        const updatedActive = updatedFeeRule?.active;

        try {
            if (updatedActive) {
                // Fee rule is active - synchronize across all active checkouts
                console.log(`Synchronizing updated fee rule "${updatedFeeRule.name}" across all active checkouts`);
                synchronizationResult = await synchronizeFeeRuleAcrossCheckouts(storeHash, updatedFeeRule, 'update');

                if (synchronizationResult.success) {
                    console.log(`Fee rule synchronization successful for "${updatedFeeRule.name}": ${synchronizationResult.successCount}/${synchronizationResult.processedCheckouts} checkouts`);
                } else {
                    console.warn(`Fee rule synchronization had errors for "${updatedFeeRule.name}": ${synchronizationResult.errorCount}/${synchronizationResult.processedCheckouts} failed`);
                }
            } else if (originalActive && !updatedActive) {
                // Fee rule was deactivated - remove from all checkouts
                console.log(`Fee rule "${updatedFeeRule.name}" deactivated - removing from all active checkouts`);
                synchronizationResult = await synchronizeFeeRuleAcrossCheckouts(storeHash, originalFeeRule, 'delete');

                if (synchronizationResult.success) {
                    console.log(`Fee rule deactivation synchronization successful for "${updatedFeeRule.name}"`);
                } else {
                    console.warn(`Fee rule deactivation synchronization had errors for "${updatedFeeRule.name}"`);
                }
            } else {
                console.log(`Fee rule "${updatedFeeRule?.name}" is inactive - no synchronization needed`);
            }
        } catch (error) {
            console.error(`Error during fee rule synchronization for "${updatedFeeRule?.name}":`, error);
            synchronizationResult = {
                success: false,
                operation: updatedActive ? 'update' : 'delete',
                processedCheckouts: 0,
                successCount: 0,
                errorCount: 1,
                error: error.message || 'Unknown synchronization error'
            };
        }

        // Determine synchronization reason for response
        let synchronizationReason = 'No synchronization needed';
        if (originalActive !== updatedActive) {
            if (!originalActive && updatedActive) {
                synchronizationReason = 'Fee rule activated - applied to all active checkouts';
            } else if (originalActive && !updatedActive) {
                synchronizationReason = 'Fee rule deactivated - removed from all active checkouts';
            }
        } else if (updatedActive) {
            synchronizationReason = 'Fee rule updated - synchronized across all active checkouts';
        } else {
            synchronizationReason = 'Fee rule is inactive - no synchronization performed';
        }

        res.status(200).json({
            success: true,
            message: 'Fee rule updated successfully',
            data: updatedFeeRule,
            synchronization: synchronizationResult ? {
                attempted: true,
                success: synchronizationResult.success,
                operation: synchronizationResult.operation,
                processedCheckouts: synchronizationResult.processedCheckouts,
                successCount: synchronizationResult.successCount,
                errorCount: synchronizationResult.errorCount,
                error: synchronizationResult.error,
                details: synchronizationResult.results || [],
                reason: synchronizationReason
            } : {
                attempted: false,
                reason: synchronizationReason
            }
        });

    } catch (error) {
        console.error('Error updating fee rule:', error);
        res.status(500).json({
            error: 'Internal server error',
            message: error.message
        });
    }
}

async function handleDeleteFeeRule(req: NextApiRequest, res: NextApiResponse) {
    try {
        // Get the session to extract store information
        const { storeHash } = await getSession(req);

        const { feeRuleId } = req.body;

        // Validate required fields
        if (!feeRuleId) {
            res.status(400).json({
                error: 'Missing required field: feeRuleId is required'
            });
            return;
        }

        // Check if fee rule exists
        const existingFeeRule = await db.getFeeRule(storeHash, feeRuleId);
        if (!existingFeeRule) {
            res.status(404).json({
                error: 'Fee rule not found'
            });
            return;
        }

        // Synchronize fee rule deletion across all active checkouts
        let synchronizationResult = null;
        try {
            console.log(`Synchronizing deletion of fee rule "${existingFeeRule.name}" across all active checkouts`);
            synchronizationResult = await synchronizeFeeRuleAcrossCheckouts(storeHash, existingFeeRule, 'delete');

            if (synchronizationResult.success) {
                console.log(`Fee rule deletion synchronization successful for "${existingFeeRule.name}": ${synchronizationResult.successCount}/${synchronizationResult.processedCheckouts} checkouts`);
            } else {
                console.warn(`Fee rule deletion synchronization had errors for "${existingFeeRule.name}": ${synchronizationResult.errorCount}/${synchronizationResult.processedCheckouts} failed`);
            }
        } catch (error) {
            console.error(`Error during fee rule deletion synchronization for "${existingFeeRule.name}":`, error);
            synchronizationResult = {
                success: false,
                operation: 'delete',
                processedCheckouts: 0,
                successCount: 0,
                errorCount: 1,
                error: error.message || 'Unknown synchronization error'
            };
        }

        // Delete the fee rule from the database
        await db.deleteFeeRule(storeHash, feeRuleId);

        res.status(200).json({
            success: true,
            message: 'Fee rule deleted successfully',
            data: { feeRuleId },
            synchronization: synchronizationResult ? {
                attempted: true,
                success: synchronizationResult.success,
                operation: synchronizationResult.operation,
                processedCheckouts: synchronizationResult.processedCheckouts,
                successCount: synchronizationResult.successCount,
                errorCount: synchronizationResult.errorCount,
                error: synchronizationResult.error,
                details: synchronizationResult.results || []
            } : {
                attempted: false,
                reason: 'Synchronization not attempted'
            }
        });

    } catch (error) {
        console.error('Error deleting fee rule:', error);
        res.status(500).json({
            error: 'Internal server error',
            message: error.message
        });
    }
}
