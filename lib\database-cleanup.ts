import db from './db';
import { getAllActiveCheckouts } from './checkout-management';

/**
 * Comprehensive database cleanup utilities for maintaining data consistency
 */

/**
 * Clean up orphaned appliedFeeHistory records across all fee rules
 * Removes history entries for checkouts that no longer exist
 * 
 * @param storeHash - Store hash for database operations
 * @returns Promise<any> - Result of cleanup operation
 */
export async function cleanupOrphanedFeeHistory(storeHash: string): Promise<any> {
    console.log(`Starting cleanup of orphaned fee history for store ${storeHash}`);

    try {
        // Get all active checkouts to determine which ones are valid
        const activeCheckouts = await getAllActiveCheckouts(storeHash);
        const activeCheckoutIds = new Set(activeCheckouts.map(checkout => checkout.checkoutId));

        // Get all fee rules for this store
        const feeRules = await db.getFeeRules(storeHash);
        
        let totalCleaned = 0;
        const cleanupResults = [];

        for (const feeRule of feeRules) {
            if (!feeRule.appliedFeeHistory || feeRule.appliedFeeHistory.length === 0) {
                continue;
            }

            // Filter out history entries for non-existent checkouts
            const validHistory = feeRule.appliedFeeHistory.filter(
                (record: any) => activeCheckoutIds.has(record.checkoutId)
            );

            const removedCount = feeRule.appliedFeeHistory.length - validHistory.length;

            if (removedCount > 0) {
                // Update the fee rule with cleaned history
                await db.updateFeeRule(storeHash, feeRule.id!, {
                    appliedFeeHistory: validHistory,
                    updated_at: Date.now()
                });

                totalCleaned += removedCount;
                cleanupResults.push({
                    feeRuleId: feeRule.id,
                    feeRuleName: feeRule.name,
                    removedRecords: removedCount,
                    remainingRecords: validHistory.length
                });

                console.log(`Cleaned ${removedCount} orphaned history records from fee rule "${feeRule.name}"`);
            }
        }

        console.log(`Completed orphaned fee history cleanup: ${totalCleaned} records removed across ${cleanupResults.length} fee rules`);

        return {
            success: true,
            totalCleaned,
            processedFeeRules: feeRules.length,
            modifiedFeeRules: cleanupResults.length,
            details: cleanupResults
        };

    } catch (error) {
        console.error(`Error during orphaned fee history cleanup for store ${storeHash}:`, error);
        throw error;
    }
}

/**
 * Clean up inactive checkouts that have been abandoned or completed
 * Removes checkouts that are older than the specified age and not active
 * 
 * @param storeHash - Store hash for database operations
 * @param maxAgeHours - Maximum age in hours for inactive checkouts (default: 24)
 * @returns Promise<any> - Result of cleanup operation
 */
export async function cleanupInactiveCheckouts(storeHash: string, maxAgeHours: number = 24): Promise<any> {
    console.log(`Starting cleanup of inactive checkouts older than ${maxAgeHours} hours for store ${storeHash}`);

    try {
        const cutoffTime = Date.now() - (maxAgeHours * 60 * 60 * 1000);
        
        // Get all checkouts (active and inactive)
        const allCheckouts = await db.getActiveCheckouts(storeHash); // This might need to be expanded to get all checkouts
        
        const inactiveCheckouts = allCheckouts.filter(checkout => 
            checkout.status !== 'active' && checkout.updatedAt < cutoffTime
        );

        let cleanedCount = 0;
        const cleanupResults = [];

        for (const checkout of inactiveCheckouts) {
            try {
                // Remove the inactive checkout
                const removed = await db.removeActiveCheckout(storeHash, checkout.checkoutId);
                
                if (removed) {
                    cleanedCount++;
                    cleanupResults.push({
                        checkoutId: checkout.checkoutId,
                        status: checkout.status,
                        age: Math.round((Date.now() - checkout.updatedAt) / (60 * 60 * 1000)) // hours
                    });

                    console.log(`Removed inactive checkout ${checkout.checkoutId} (status: ${checkout.status})`);
                }
            } catch (error) {
                console.error(`Error removing inactive checkout ${checkout.checkoutId}:`, error);
            }
        }

        console.log(`Completed inactive checkout cleanup: ${cleanedCount} checkouts removed`);

        return {
            success: true,
            cleanedCount,
            totalInactiveFound: inactiveCheckouts.length,
            maxAgeHours,
            details: cleanupResults
        };

    } catch (error) {
        console.error(`Error during inactive checkout cleanup for store ${storeHash}:`, error);
        throw error;
    }
}

/**
 * Comprehensive database maintenance for a store
 * Runs all cleanup operations in sequence
 * 
 * @param storeHash - Store hash for database operations
 * @param options - Cleanup options
 * @returns Promise<any> - Result of all cleanup operations
 */
export async function performDatabaseMaintenance(
    storeHash: string, 
    options: {
        cleanupOrphanedHistory?: boolean;
        cleanupInactiveCheckouts?: boolean;
        maxCheckoutAgeHours?: number;
    } = {}
): Promise<any> {
    console.log(`Starting comprehensive database maintenance for store ${storeHash}`);

    const {
        cleanupOrphanedHistory = true,
        cleanupInactiveCheckouts = true,
        maxCheckoutAgeHours = 24
    } = options;

    const results = {
        success: true,
        operations: [],
        totalTime: 0,
        errors: []
    };

    const startTime = Date.now();

    try {
        // Clean up orphaned fee history
        if (cleanupOrphanedHistory) {
            try {
                console.log('Running orphaned fee history cleanup...');
                const historyResult = await cleanupOrphanedFeeHistory(storeHash);
                results.operations.push({
                    operation: 'cleanupOrphanedHistory',
                    success: true,
                    result: historyResult
                });
            } catch (error) {
                console.error('Error in orphaned history cleanup:', error);
                results.success = false;
                results.errors.push({
                    operation: 'cleanupOrphanedHistory',
                    error: error.message
                });
            }
        }

        // Clean up inactive checkouts
        if (cleanupInactiveCheckouts) {
            try {
                console.log('Running inactive checkout cleanup...');
                const checkoutResult = await cleanupInactiveCheckouts(storeHash, maxCheckoutAgeHours);
                results.operations.push({
                    operation: 'cleanupInactiveCheckouts',
                    success: true,
                    result: checkoutResult
                });
            } catch (error) {
                console.error('Error in inactive checkout cleanup:', error);
                results.success = false;
                results.errors.push({
                    operation: 'cleanupInactiveCheckouts',
                    error: error.message
                });
            }
        }

        results.totalTime = Date.now() - startTime;
        
        console.log(`Completed database maintenance for store ${storeHash} in ${results.totalTime}ms`);
        console.log(`Operations: ${results.operations.length}, Errors: ${results.errors.length}`);

        return results;

    } catch (error) {
        console.error(`Error during database maintenance for store ${storeHash}:`, error);
        results.success = false;
        results.totalTime = Date.now() - startTime;
        results.errors.push({
            operation: 'general',
            error: error.message
        });
        return results;
    }
}

/**
 * Emergency cleanup function for when a checkout is known to be deleted
 * but may have left orphaned data
 * 
 * @param storeHash - Store hash for database operations
 * @param checkoutId - ID of the deleted checkout
 * @returns Promise<any> - Result of emergency cleanup
 */
export async function emergencyCheckoutCleanup(storeHash: string, checkoutId: string): Promise<any> {
    console.log(`Starting emergency cleanup for checkout ${checkoutId} in store ${storeHash}`);

    try {
        // Use the existing cleanup function from checkout-management
        const { cleanupDeletedCheckout } = await import('./checkout-management');
        const result = await cleanupDeletedCheckout(storeHash, checkoutId);

        console.log(`Emergency cleanup completed for checkout ${checkoutId}`);
        return result;

    } catch (error) {
        console.error(`Error during emergency cleanup for checkout ${checkoutId}:`, error);
        throw error;
    }
}
