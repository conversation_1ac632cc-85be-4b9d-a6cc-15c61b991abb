import { NextApiRequest, NextApiResponse } from 'next';
import { bigcommerceClient, getSession } from '../../../lib/auth';

export interface CustomerGroup {
    id: number;
    name: string;
}

export default async function customerGroups(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== 'GET') {
        res.setHeader('Allow', ['GET']);
        res.status(405).json({ error: `Method ${req.method} Not Allowed` });
        return;
    }

    try {
        console.log("Res : ",res);
        const { storeHash, accessToken } = await getSession(req);
        const bigcommerce = bigcommerceClient(accessToken, storeHash, 'v2');

        console.log(`Fetching customer groups for store ${storeHash}`);

        // Fetch customer groups from BigCommerce API (v2)
        const response = await bigcommerce.get('/customer_groups');
        console.log("customer groups", response);
        
        if (!response) {
            throw new Error('No data received from BigCommerce customer groups API');
        }

        // Transform the response to include only id and name fields
        const customerGroups: CustomerGroup[] = response.map((group: any) => ({
            id: group.id,
            name: group.name
        }));

        console.log(`Successfully fetched ${customerGroups.length} customer groups`);

        res.status(200).json({
            success: true,
            data: customerGroups,
            count: customerGroups.length
        });

    } catch (error) {
        console.error('Error fetching customer groups:', error);
        
        // Handle specific BigCommerce API errors
        if (error.response) {
            const { status, data } = error.response;
            console.error(`BigCommerce API error ${status}:`, data);
            
            // Handle common error scenarios
            if (status === 403) {
                res.status(200).json({
                    success: false,
                    error: 'Insufficient permissions to access customer groups data',
                    fallback: true,
                    data: []
                });
                return;
            }
            
            if (status === 404) {
                res.status(200).json({
                    success: true,
                    data: [],
                    count: 0,
                    message: 'No customer groups found'
                });
                return;
            }
        }

        // Provide fallback response for any other errors
        res.status(200).json({
            success: false,
            error: error.message || 'Failed to fetch customer groups',
            fallback: true,
            data: []
        });
    }
}