import React, { useState, useEffect, useMemo } from 'react';
import {
    Box,
    Button,
    Checkbox,
    FormGroup,
    Input,
    Select,
    Text,
    Flex,
} from '@bigcommerce/big-design';
import { ArrowBackIcon, ArrowForwardIcon } from '@bigcommerce/big-design-icons';
import { useProductsForFilter } from '../lib/hooks';

export interface ProductFilterItem {
    id: number;
    name: string;
}

interface ProductFilterProps {
    selectedIds: number[];
    onChange: (selectedIds: number[]) => void;
    error?: string;
}

const ProductFilter: React.FC<ProductFilterProps> = ({
    selectedIds,
    onChange,
    error
}) => {
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage, setItemsPerPage] = useState(10);
    const [searchTerm, setSearchTerm] = useState('');
    const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');

    // Debounce search term
    useEffect(() => {
        const timer = setTimeout(() => {
            setDebouncedSearchTerm(searchTerm);
            setCurrentPage(1); // Reset to first page when searching
        }, 300);

        return () => clearTimeout(timer);
    }, [searchTerm]);

    // Load all products at once
    const {
        products,
        meta,
        isLoading,
        error: apiError,
        fallback,
        loadedAll,
        hasMoreProducts,
        totalAvailable,
        maxProductsLimit
    } = useProductsForFilter(true);

    // Client-side filtering across all products
    const filteredProducts = useMemo(() => {
        if (!debouncedSearchTerm) {
            return products;
        }

        return products.filter(product =>
            product.name?.toLowerCase().includes(debouncedSearchTerm.toLowerCase())
        );
    }, [products, debouncedSearchTerm]);

    // Client-side pagination
    const totalItems = filteredProducts.length;
    const totalPages = Math.ceil(totalItems / itemsPerPage);

    // Ensure current page is valid after filtering
    const validCurrentPage = Math.min(currentPage, Math.max(1, totalPages));
    const startIndex = (validCurrentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const currentPageProducts = filteredProducts.slice(startIndex, endIndex);

    const startItem = totalItems > 0 ? startIndex + 1 : 0;
    const endItem = Math.min(endIndex, totalItems);

    // Update current page if it's invalid
    useEffect(() => {
        if (validCurrentPage !== currentPage) {
            setCurrentPage(validCurrentPage);
        }
    }, [validCurrentPage, currentPage]);

    const handleSelectAll = (checked: boolean) => {
        if (checked) {
            // Add all visible products to selection (don't remove existing selections from other pages)
            const newSelections = currentPageProducts.map(product => product.id);
            const combinedSelections = Array.from(new Set([...selectedIds, ...newSelections]));
            onChange(combinedSelections);
        } else {
            // Remove all visible products from selection
            const visibleProductIds = currentPageProducts.map(product => product.id);
            const remainingSelections = selectedIds.filter(id => !visibleProductIds.includes(id));
            onChange(remainingSelections);
        }
    };

    const handleItemChange = (productId: number, checked: boolean) => {
        if (checked) {
            onChange([...selectedIds, productId]);
        } else {
            onChange(selectedIds.filter(id => id !== productId));
        }
    };

    const handleItemsPerPageChange = (value: string) => {
        setItemsPerPage(Number(value));
        setCurrentPage(1);
    };

    const handlePreviousPage = (e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();
        if (currentPage > 1) {
            setCurrentPage(currentPage - 1);
        }
    };

    const handleNextPage = (e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();
        if (currentPage < totalPages) {
            setCurrentPage(currentPage + 1);
        }
    };

    const visibleProductIds = currentPageProducts.map(product => product.id);
    const isAllVisibleSelected = visibleProductIds.length > 0 &&
        visibleProductIds.every(id => selectedIds.includes(id));
    const isIndeterminate = visibleProductIds.some(id => selectedIds.includes(id)) &&
        !isAllVisibleSelected;

    return (
        <FormGroup>
            <Text bold>Products</Text>
            <Text color="secondary60" marginBottom="small">
                Select specific products to apply this fee to. If no products are selected, the fee will apply to all products (subject to other filters).
            </Text>

            {(error || apiError) && (
                <Text color="danger" marginBottom="small">
                    {error || apiError}
                </Text>
            )}

            {fallback && (
                <Text color="warning" marginBottom="small">
                    Limited access to products data. Using sample data for demonstration.
                </Text>
            )}

            {hasMoreProducts && (
                <Text color="warning" marginBottom="small">
                    Showing first {maxProductsLimit} of {totalAvailable} products. Consider using search to find specific products.
                </Text>
            )}

            {/* Search Input */}
            <Box marginBottom="medium">
                <Input
                    placeholder="Search products by name..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    disabled={isLoading}
                />
            </Box>

            {/* Pagination Controls */}
            <Flex justifyContent="space-between" alignItems="center" marginBottom="medium">
                <Box>
                    <Select
                        value={String(itemsPerPage)}
                        onOptionChange={handleItemsPerPageChange}
                        disabled={isLoading}
                        options={[
                            { value: '10', content: 'Show 10 items' },
                            { value: '20', content: 'Show 20 items' },
                            { value: '30', content: 'Show 30 items' },
                            { value: '40', content: 'Show 40 items' },
                        ]}
                    />
                </Box>

                <Text color="secondary60">
                    Showing {totalItems > 0 ? startItem : 0}-{endItem} of {totalItems} products
                </Text>

                <Flex alignItems="center">
                    <Button
                        variant="subtle"
                        iconOnly={<ArrowBackIcon />}
                        onClick={handlePreviousPage}
                        disabled={currentPage <= 1 || isLoading}
                        marginRight="xSmall"
                        type="button"
                    />
                    <Text marginLeft="small" marginRight="small">
                        Page {currentPage} of {totalPages || 1}
                    </Text>
                    <Button
                        variant="subtle"
                        iconOnly={<ArrowForwardIcon />}
                        onClick={handleNextPage}
                        disabled={currentPage >= totalPages || isLoading}
                        marginLeft="xSmall"
                        type="button"
                    />
                </Flex>
            </Flex>

            {/* Product List with Loading Overlay */}
            <Box
                border="box"
                borderRadius="normal"
                padding="medium"
                style={{
                    maxHeight: '300px',
                    overflowY: 'auto',
                    position: 'relative'
                }}
            >
                {/* Loading Overlay */}
                {isLoading && (
                    <Box
                        style={{
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            right: 0,
                            bottom: 0,
                            backgroundColor: 'rgba(255, 255, 255, 0.8)',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            zIndex: 10,
                            borderRadius: '4px'
                        }}
                    >
                        <Text color="secondary60">Loading products...</Text>
                    </Box>
                )}

                {currentPageProducts.length === 0 && !isLoading ? (
                    <Text color="secondary60">
                        {debouncedSearchTerm ? 'No products found matching your search.' : 'No products available'}
                    </Text>
                ) : (
                    <>
                        {/* Select All checkbox */}
                        <Box marginBottom="small" paddingBottom="small" borderBottom="box">
                            <Checkbox
                                label="Select All (visible)"
                                checked={isAllVisibleSelected}
                                isIndeterminate={isIndeterminate}
                                onChange={(e) => handleSelectAll(e.target.checked)}
                                disabled={isLoading}
                            />
                        </Box>

                        {/* Individual product checkboxes */}
                        {currentPageProducts.map((product) => (
                            <Box key={product.id} marginBottom="xSmall">
                                <Checkbox
                                    label={product.name}
                                    checked={selectedIds.includes(product.id)}
                                    onChange={(e) => handleItemChange(product.id, e.target.checked)}
                                    disabled={isLoading}
                                />
                            </Box>
                        ))}
                    </>
                )}
            </Box>

            {selectedIds.length > 0 && (
                <Text color="secondary60" marginTop="xSmall">
                    {selectedIds.length} product{selectedIds.length !== 1 ? 's' : ''} selected
                </Text>
            )}
        </FormGroup>
    );
};

export default ProductFilter;
