import { Box, Button, HR, Text } from '@bigcommerce/big-design';
import { ArrowBackIcon } from '@bigcommerce/big-design-icons';
import { useRouter } from 'next/router';
import { TabIds, TabRoutes } from './header';

const InnerHeader = () => {
    const router = useRouter();

    // Navigate to Order Fee tab as the main navigation
    const handleBackClick = () => router.push(TabRoutes[TabIds.ORDER_FEE]);

    return (
        <Box marginBottom="xxLarge">
            <Button iconLeft={<ArrowBackIcon color="secondary50" />} variant="subtle" onClick={handleBackClick}>
                <Text bold color="secondary50">Back to Order Fee</Text>
            </Button>
            <HR color="secondary30" />
        </Box>
    );
};

export default InnerHeader;
