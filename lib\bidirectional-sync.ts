import { applyFeesToCheckout, getActiveFeeRules } from './fee-application';
import { getAllActiveCheckouts, recalculateCheckoutFees } from './checkout-management';
import { withRetry, safeExecute, createErrorContext, Logger, DEFAULT_RETRY_OPTIONS } from './error-handling';
import { FeeRuleData } from '../types/db';
import db from './db';

/**
 * Apply fee rule changes to all active checkouts
 * Direction 1: Fee Rule Changes → Checkout Updates
 * 
 * This function is called when fee rules are created, updated, or deleted
 * and ensures all active checkouts reflect the current fee rule state
 * 
 * @param storeHash - Store hash for database operations
 * @param operation - Type of operation: 'create', 'update', 'delete'
 * @param feeRuleId - ID of the fee rule that changed (optional for create)
 * @returns Promise<any> - Result of applying changes to all checkouts
 */
export async function applyFeesToAllActiveCheckouts(
    storeHash: string,
    operation: 'create' | 'update' | 'delete',
    feeRuleId?: string
): Promise<any> {
    const logger = new Logger('BidirectionalSync');
    const context = createErrorContext('apply_fees_to_all_checkouts', storeHash, undefined, feeRuleId, { operation });

    logger.info(`Starting bidirectional sync: applying fee rule ${operation} to all active checkouts in store ${storeHash}`, {
        operation,
        feeRuleId
    });

    // Use retry mechanism for the entire synchronization operation
    const result = await withRetry(async () => {
        // Get all active checkouts for this store with error handling
        const checkoutsResult = await safeExecute(
            () => getAllActiveCheckouts(storeHash),
            createErrorContext('get_all_active_checkouts', storeHash)
        );

        if (!checkoutsResult.success) {
            throw new Error(`Failed to get active checkouts: ${checkoutsResult.error}`);
        }

        const activeCheckouts = checkoutsResult.data || [];

        if (activeCheckouts.length === 0) {
            logger.info(`No active checkouts found for store ${storeHash}`);
            return {
                success: true,
                message: 'No active checkouts to synchronize',
                processedCheckouts: 0,
                operation
            };
        }

        logger.info(`Found ${activeCheckouts.length} active checkouts to synchronize`);

        const results = [];
        let successCount = 0;
        let errorCount = 0;

        // Process each checkout with individual error handling
        for (const checkout of activeCheckouts) {
            const checkoutContext = createErrorContext('process_checkout', storeHash, checkout.checkoutId, feeRuleId, { operation });

            const checkoutResult = await safeExecute(async () => {
                logger.debug(`Processing checkout ${checkout.checkoutId} for ${operation} operation`);

                // Recalculate fees for this checkout (removes existing, applies fresh)
                const recalculationResult = await recalculateCheckoutFees(storeHash, checkout.checkoutId);

                // Update checkout record with new applied fees
                await db.updateActiveCheckout(storeHash, checkout.checkoutId, {
                    appliedFees: recalculationResult.appliedFeeIds || [],
                    updatedAt: Date.now()
                });

                logger.debug(`Successfully synchronized checkout ${checkout.checkoutId}: ${recalculationResult.appliedFeesCount} fees applied`);

                return {
                    checkoutId: checkout.checkoutId,
                    success: true,
                    appliedFeesCount: recalculationResult.appliedFeesCount,
                    operation
                };
            }, checkoutContext);

            if (checkoutResult.success) {
                results.push(checkoutResult.data);
                successCount++;
            } else {
                logger.warn(`Failed to synchronize checkout ${checkout.checkoutId}`, { error: checkoutResult.error });
                results.push({
                    checkoutId: checkout.checkoutId,
                    success: false,
                    error: checkoutResult.error,
                    operation
                });
                errorCount++;
            }
        }

        const overallSuccess = errorCount === 0;
        logger.info(`Completed bidirectional sync for ${operation}: ${successCount} successful, ${errorCount} errors`);

        return {
            success: overallSuccess,
            operation,
            processedCheckouts: activeCheckouts.length,
            successCount,
            errorCount,
            results,
            message: `Fee rule ${operation} synchronized across ${successCount}/${activeCheckouts.length} checkouts`
        };

    }, context, DEFAULT_RETRY_OPTIONS.feeSync);

    if (!result.success) {
        logger.error(`Failed to complete bidirectional sync after retries`, new Error(result.error), {
            operation,
            feeRuleId,
            retryCount: result.retryCount,
            duration: result.duration
        });
        throw new Error(result.error);
    }

    return result.data;
}

/**
 * Recalculate fees for a specific checkout based on current fee rules
 * Direction 2: Checkout Changes → Fee Recalculation
 * 
 * This function is called when a checkout is updated and ensures
 * the checkout has the correct fees based on current fee rules
 * 
 * @param storeHash - Store hash for database operations
 * @param checkoutId - ID of the checkout to recalculate
 * @returns Promise<any> - Result of fee recalculation
 */
export async function recalculateFeesForCheckout(
    storeHash: string,
    checkoutId: string
): Promise<any> {
    console.log(`Starting checkout-driven sync: recalculating fees for checkout ${checkoutId} in store ${storeHash}`);

    try {
        // Use the existing recalculateCheckoutFees function
        const result = await recalculateCheckoutFees(storeHash, checkoutId);

        // Update the checkout record with new applied fees
        await db.updateActiveCheckout(storeHash, checkoutId, {
            appliedFees: result.appliedFeeIds || [],
            updatedAt: Date.now()
        });

        console.log(`Successfully recalculated fees for checkout ${checkoutId}: ${result.appliedFeesCount} fees applied`);

        return {
            ...result,
            checkoutId,
            message: `Checkout fees recalculated: ${result.appliedFeesCount} fees applied`
        };

    } catch (error) {
        console.error(`Error recalculating fees for checkout ${checkoutId}:`, error);
        throw error;
    }
}

/**
 * Synchronize a specific fee rule across all active checkouts
 * Used when a single fee rule is modified
 * 
 * @param storeHash - Store hash for database operations
 * @param feeRule - The fee rule to synchronize
 * @param operation - Type of operation: 'create', 'update', 'delete'
 * @returns Promise<any> - Result of synchronization
 */
export async function synchronizeFeeRuleAcrossCheckouts(
    storeHash: string,
    feeRule: FeeRuleData,
    operation: 'create' | 'update' | 'delete'
): Promise<any> {
    console.log(`Synchronizing fee rule "${feeRule.name}" (${operation}) across all active checkouts`);

    try {
        // For all operations, we recalculate all fees to ensure consistency
        // This handles create (adds new fee), update (modifies existing), and delete (removes fee)
        const syncResult = await applyFeesToAllActiveCheckouts(storeHash, operation, feeRule.id);

        return {
            ...syncResult,
            feeRuleId: feeRule.id,
            feeRuleName: feeRule.name,
            message: `Fee rule "${feeRule.name}" ${operation} synchronized across ${syncResult.successCount}/${syncResult.processedCheckouts} checkouts`
        };

    } catch (error) {
        console.error(`Error synchronizing fee rule "${feeRule.name}" (${operation}):`, error);
        throw error;
    }
}

/**
 * Full synchronization of all fee rules across all active checkouts
 * Used for system maintenance or when data consistency needs to be ensured
 * 
 * @param storeHash - Store hash for database operations
 * @returns Promise<any> - Result of full synchronization
 */
export async function fullFeeRuleSynchronization(storeHash: string): Promise<any> {
    console.log(`Starting full fee rule synchronization for store ${storeHash}`);

    try {
        // Get all active checkouts and fee rules
        const [activeCheckouts, activeFeeRules] = await Promise.all([
            getAllActiveCheckouts(storeHash),
            getActiveFeeRules(storeHash)
        ]);

        if (activeCheckouts.length === 0) {
            return {
                success: true,
                message: 'No active checkouts to synchronize',
                processedCheckouts: 0,
                activeFeeRules: activeFeeRules.length
            };
        }

        console.log(`Full sync: ${activeCheckouts.length} checkouts, ${activeFeeRules.length} active fee rules`);

        // Use the existing function to apply to all checkouts
        const syncResult = await applyFeesToAllActiveCheckouts(storeHash, 'update');

        return {
            ...syncResult,
            activeFeeRules: activeFeeRules.length,
            message: `Full synchronization completed: ${syncResult.successCount}/${syncResult.processedCheckouts} checkouts synchronized with ${activeFeeRules.length} fee rules`
        };

    } catch (error) {
        console.error(`Error in full fee rule synchronization for store ${storeHash}:`, error);
        throw error;
    }
}
