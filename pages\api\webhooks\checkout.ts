import { NextApiRequest, NextApiResponse } from 'next';
import { verifyWebhook, parseCheckoutWebhookPayload, extractStoreHashFromStoreId } from '../../../lib/webhook-verification';
import { CheckoutWebhookPayload } from '../../../types/bigcommerce';
import { handleCheckoutCreated, handleCheckoutUpdated, handleCheckoutDeleted } from '../../../lib/webhook-handlers';

/**
 * Main webhook handler for BigCommerce checkout events
 * Handles: store/checkout/created, store/checkout/updated, store/checkout/deleted
 */
export default async function checkoutWebhook(req: NextApiRequest, res: NextApiResponse) {
    // Only accept POST requests
    if (req.method !== 'POST') {
        res.setHeader('Allow', ['POST']);
        res.status(405).json({ error: `Method ${req.method} Not Allowed` });
        return;
    }

    try {
        // Get raw body and signature
        const rawBody = typeof req.body === 'string' ? req.body : JSON.stringify(req.body);
        const signature = req.headers['x-bc-webhook-signature'] as string;

        if (!signature) {
            console.error('Missing webhook signature');
            res.status(400).json({ error: 'Missing webhook signature' });
            return;
        }

        // Verify webhook authenticity
        const isValid = verifyWebhook({
            payload: rawBody,
            signature,
            timestamp: req.body.created_at
        });

        if (!isValid) {
            console.error('Webhook verification failed');
            res.status(401).json({ error: 'Webhook verification failed' });
            return;
        }

        // Parse webhook payload
        const webhookPayload = parseCheckoutWebhookPayload(rawBody);
        if (!webhookPayload) {
            console.error('Invalid webhook payload');
            res.status(400).json({ error: 'Invalid webhook payload' });
            return;
        }

        // Extract store hash from store ID
        const storeHash = extractStoreHashFromStoreId(webhookPayload.store_id);
        const checkoutId = webhookPayload.data.id;

        console.log(`Processing ${webhookPayload.scope} webhook for checkout ${checkoutId} in store ${storeHash}`);

        // Route to appropriate handler based on webhook scope
        let result;
        switch (webhookPayload.scope) {
            case 'store/checkout/created':
                result = await handleCheckoutCreated(storeHash, checkoutId, webhookPayload);
                break;
            
            case 'store/checkout/updated':
                result = await handleCheckoutUpdated(storeHash, checkoutId, webhookPayload);
                break;
            
            case 'store/checkout/deleted':
                result = await handleCheckoutDeleted(storeHash, checkoutId, webhookPayload);
                break;
            
            default:
                console.error(`Unsupported webhook scope: ${webhookPayload.scope}`);
                res.status(400).json({ error: `Unsupported webhook scope: ${webhookPayload.scope}` });
                return;
        }

        // Return success response
        res.status(200).json({
            success: true,
            message: `Successfully processed ${webhookPayload.scope} webhook`,
            data: {
                checkoutId,
                storeHash,
                scope: webhookPayload.scope,
                result
            }
        });

    } catch (error) {
        console.error('Error processing checkout webhook:', error);
        res.status(500).json({ 
            error: 'Internal server error processing webhook',
            message: error.message 
        });
    }
}

// Configure body parsing for webhook signature verification
export const config = {
    api: {
        bodyParser: {
            sizeLimit: '1mb',
        },
    },
}
