import { NextApiRequest, NextApiResponse } from 'next';
import { bigcommerceClient, getSession } from '../../../lib/auth';

export interface Product {
    id: number;
    name: string;
    sku?: string;
    price?: number;
    is_visible?: boolean;
}

interface ProductsResponse {
    data: Product[];
    meta?: {
        pagination?: {
            total: number;
            count: number;
            per_page: number;
            current_page: number;
            total_pages: number;
        };
    };
    fallback?: boolean;
    error?: string;
}

export default async function products(req: NextApiRequest, res: NextApiResponse<ProductsResponse>) {
    if (req.method !== 'GET') {
        res.setHeader('Allow', ['GET']);
        res.status(405).json({
            data: [],
            fallback: true,
            error: `Method ${req.method} Not Allowed`
        });
        return;
    }

    try {
        const { accessToken, storeHash } = await getSession(req);
        const bigcommerce = bigcommerceClient(accessToken, storeHash);

        // Extract query parameters
        const {
            loadAll = 'false',
            search = ''
        } = req.query;

        // Determine if we should load all products or use pagination
        const shouldLoadAll = loadAll === 'true';
        const maxProducts = 1000; // Reasonable limit for performance

        // Build query parameters for BigCommerce API
        const queryParams = new URLSearchParams({
            limit: shouldLoadAll ? String(maxProducts) : '10',
            include_fields: 'id,name,sku,price,is_visible',
            is_visible: 'true' // Only fetch visible products by default
        });

        // Add search parameter if provided (for server-side search when not loading all)
        if (!shouldLoadAll && search && typeof search === 'string' && search.trim()) {
            queryParams.append('keyword', search.trim());
        }

        console.log(`Fetching products from BigCommerce API: /catalog/products?${queryParams.toString()}`);

        const response = await bigcommerce.get(`/catalog/products?${queryParams.toString()}`);
        
        console.log(`Products API response:`, {
            dataLength: response.data?.length || 0,
            meta: response.meta
        });

        // Transform the response to match our interface
        const transformedData: Product[] = (response.data || []).map((product: any) => ({
            id: product.id,
            name: product.name,
            sku: product.sku,
            price: product.price,
            is_visible: product.is_visible
        }));

        // Check if we hit the limit when loading all products
        const totalProducts = response.meta?.pagination?.total || transformedData.length;
        const hasMoreProducts = shouldLoadAll && totalProducts > maxProducts;

        res.status(200).json({
            data: transformedData,
            meta: {
                ...response.meta,
                loadedAll: shouldLoadAll,
                hasMoreProducts,
                maxProductsLimit: maxProducts,
                totalAvailable: totalProducts
            }
        });

    } catch (error: any) {
        console.error('Products API error:', error);
        
        const errorMessage = error?.response?.data?.title || 
                           error?.message || 
                           'Failed to fetch products';
        
        const statusCode = error?.response?.status || 500;

        // Provide fallback data for common scenarios
        if (statusCode === 401 || statusCode === 403) {
            console.log('Products API: Access denied, providing fallback data');
            res.status(200).json({
                data: [
                    { id: 1, name: 'Sample Product 1' },
                    { id: 2, name: 'Sample Product 2' },
                    { id: 3, name: 'Sample Product 3' }
                ],
                fallback: true,
                error: 'Limited access to products data. Using sample data.'
            });
            return;
        }

        // For other errors, return error response
        res.status(statusCode).json({
            data: [],
            fallback: true,
            error: errorMessage
        });
    }
}
