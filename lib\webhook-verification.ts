import crypto from 'crypto';
import { WebhookVerificationData, CheckoutWebhookPayload } from '../types/bigcommerce';

const { CLIENT_SECRET } = process.env;

if (!CLIENT_SECRET) {
    throw new Error('CLIENT_SECRET environment variable is required for webhook verification');
}

/**
 * Verify BigCommerce webhook signature using HMAC-SHA256
 * 
 * BigCommerce sends webhooks with an X-BC-Webhook-Signature header containing
 * the HMAC-SHA256 hash of the request body using the app's client secret
 * 
 * @param verificationData - Webhook payload and signature data
 * @returns boolean - True if signature is valid
 */
export function verifyWebhookSignature(verificationData: WebhookVerificationData): boolean {
    const { payload, signature } = verificationData;

    if (!payload || !signature) {
        console.error('Missing payload or signature for webhook verification');
        return false;
    }

    try {
        // Create HMAC using client secret
        const hmac = crypto.createHmac('sha256', CLIENT_SECRET);
        hmac.update(payload, 'utf8');
        const expectedSignature = hmac.digest('hex');

        // Compare signatures using timing-safe comparison
        const providedSignature = signature.replace('sha256=', '');
        
        return crypto.timingSafeEqual(
            Buffer.from(expectedSignature, 'hex'),
            Buffer.from(providedSignature, 'hex')
        );
    } catch (error) {
        console.error('Error verifying webhook signature:', error);
        return false;
    }
}

/**
 * Parse and validate BigCommerce checkout webhook payload
 * 
 * @param rawPayload - Raw webhook payload string
 * @returns CheckoutWebhookPayload | null - Parsed payload or null if invalid
 */
export function parseCheckoutWebhookPayload(rawPayload: string): CheckoutWebhookPayload | null {
    try {
        const payload = JSON.parse(rawPayload);

        // Validate required fields
        if (!payload.scope || !payload.store_id || !payload.data) {
            console.error('Invalid webhook payload: missing required fields');
            return null;
        }

        // Validate checkout-specific fields
        if (!payload.data.type || payload.data.type !== 'checkout') {
            console.error('Invalid webhook payload: not a checkout webhook');
            return null;
        }

        if (!payload.data.id) {
            console.error('Invalid webhook payload: missing checkout ID');
            return null;
        }

        // Validate scope is checkout-related
        const validScopes = ['store/checkout/created', 'store/checkout/updated', 'store/checkout/deleted'];
        if (!validScopes.includes(payload.scope)) {
            console.error(`Invalid webhook scope: ${payload.scope}`);
            return null;
        }

        return payload as CheckoutWebhookPayload;
    } catch (error) {
        console.error('Error parsing webhook payload:', error);
        return null;
    }
}

/**
 * Extract store hash from BigCommerce store ID
 * 
 * BigCommerce webhooks include store_id as a numeric value,
 * but we need the store hash for our database operations
 * 
 * @param storeId - Numeric store ID from webhook
 * @returns string - Store hash for database operations
 */
export function extractStoreHashFromStoreId(storeId: string | number): string {
    // For now, we'll need to maintain a mapping or use the store ID directly
    // In a production system, you might need to query your database to map store_id to store_hash
    return String(storeId);
}

/**
 * Validate webhook timestamp to prevent replay attacks
 * 
 * @param timestamp - Webhook timestamp
 * @param maxAgeSeconds - Maximum age in seconds (default: 300 = 5 minutes)
 * @returns boolean - True if timestamp is within acceptable range
 */
export function validateWebhookTimestamp(timestamp: number, maxAgeSeconds: number = 300): boolean {
    if (!timestamp) {
        return false;
    }

    const now = Math.floor(Date.now() / 1000);
    const age = now - timestamp;

    return age >= 0 && age <= maxAgeSeconds;
}

/**
 * Complete webhook verification including signature and timestamp validation
 * 
 * @param verificationData - Complete webhook verification data
 * @returns boolean - True if webhook is valid and authentic
 */
export function verifyWebhook(verificationData: WebhookVerificationData): boolean {
    // Verify signature
    if (!verifyWebhookSignature(verificationData)) {
        console.error('Webhook signature verification failed');
        return false;
    }

    // Verify timestamp if provided
    if (verificationData.timestamp && !validateWebhookTimestamp(verificationData.timestamp)) {
        console.error('Webhook timestamp validation failed');
        return false;
    }

    return true;
}
