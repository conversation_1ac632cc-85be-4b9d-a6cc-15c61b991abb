import { Box, Tabs } from '@bigcommerce/big-design';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import InnerHeader from './innerHeader';

export const TabIds = {
    HOME: 'home',
    PRODUCTS: 'products',
    ORDER_FEE: 'order-fee',
};

export const TabRoutes = {
    [TabIds.HOME]: '/', // Preserved for potential future use
    [TabIds.PRODUCTS]: '/products', // Preserved for potential future use
    [TabIds.ORDER_FEE]: '/order-fee',
};

const HeaderlessRoutes = [
    '/orders/[orderId]',
    '/orders/[orderId]/labels',
    '/orders/[orderId]/modal',
    '/productAppExtension/[productId]',
];

const InnerRoutes = [
    // '/products/[pid]', // Removed since individual product pages are deleted
    '/create-fee',
    '/edit-fee/[feeId]',
];

const HeaderTypes = {
    GLOBAL: 'global',
    INNER: 'inner',
    HEADERLESS: 'headerless',
};

const Header = () => {
    const [activeTab, setActiveTab] = useState<string>('');
    const [headerType, setHeaderType] = useState<string>(HeaderTypes.GLOBAL);
    const router = useRouter();
    const { pathname } = router;

    useEffect(() => {
        if (InnerRoutes.includes(pathname)) {
            // Use InnerHeader if route matches inner routes
            setHeaderType(HeaderTypes.INNER);
        } else if (HeaderlessRoutes.includes(pathname)) {
            setHeaderType(HeaderTypes.HEADERLESS);
        } else {
            // Check if new route matches TabRoutes
            const tabKey = Object.keys(TabRoutes).find(key => TabRoutes[key] === pathname);

            // Set the active tab to tabKey or set no active tab if route doesn't match (404)
            // Note: Root path '/' now redirects server-side to '/order-fee', so no special handling needed
            setActiveTab(tabKey ?? '');
            setHeaderType(HeaderTypes.GLOBAL);
        }

    }, [pathname]);

    useEffect(() => {
        // Prefetch order-fee page to reduce latency (doesn't prefetch in dev)
        // Note: Home and Products prefetching commented out as tabs are hidden from UI
        // router.prefetch('/products');
        router.prefetch('/order-fee');
    });

    const items = [
        // Home and Products tabs hidden from UI but functionality preserved
        // { ariaControls: 'home', id: TabIds.HOME, title: 'Home' },
        // { ariaControls: 'products', id: TabIds.PRODUCTS, title: 'Products' },
        { ariaControls: 'order-fee', id: TabIds.ORDER_FEE, title: 'Order Fee' },
    ];

    const handleTabClick = (tabId: string) => {
        setActiveTab(tabId);

        return router.push(TabRoutes[tabId]);
    };

    if (headerType === HeaderTypes.HEADERLESS) return null;
    if (headerType === HeaderTypes.INNER) return <InnerHeader />;

    return (
        <Box marginBottom="xxLarge">
            <Tabs
                activeTab={activeTab}
                items={items}
                onTabClick={handleTabClick}
            />
        </Box>
    );
};

export default Header;
