import db from './db';
import { getCheckoutDetails, updateCheckout<PERSON>ee, deleteCheckout<PERSON>ee, applyCheckoutFee, getProductDetails } from './bigcommerce-api';
import { convertFeeRuleToRequest, applyFeeRuleToMostRecentCheckout, calculateFilteredFeeCost } from './fee-application';
import { FeeRuleData, AppliedFeeRecord } from '../types/db';
import { FeeResponse, CheckoutData } from '../types/bigcommerce';

// Import the new bidirectional sync functions
import { synchronizeFeeRuleAcrossCheckouts } from './bidirectional-sync';

/**
 * Extract channel ID from channel data, handling both object and array formats
 * @param channelData - Channel data that can be an object, array, or undefined
 * @returns Channel ID as number or undefined
 */
function extractChannelId(channelData: any): number | undefined {
    if (!channelData) {
        return undefined;
    }

    // Handle array format: [ { id: 1 } ]
    if (Array.isArray(channelData) && channelData.length > 0) {
        const firstChannel = channelData[0];
        return firstChannel?.id || firstChannel?.channel_id;
    }

    // Handle object format: { id: 1 } or { channel_id: 1 }
    if (typeof channelData === 'object') {
        return channelData.id || channelData.channel_id;
    }

    return undefined;
}

/**
 * Comprehensive fee rule matching validation following the exact logic from fee-application.ts
 * Implements the shouldApplyFee logic with "apply to all" behavior for empty filters
 *
 * @param storeHash - BigCommerce store hash for API authentication
 * @param checkoutData - Checkout data containing line items
 * @param feeRule - Fee rule with comprehensive matching criteria
 * @param customerGroupId - Customer group ID for validation
 * @param channelId - Channel ID for validation
 * @returns Promise<{shouldApply: boolean, reason: string}> - Validation result with reason
 */
async function validateFeeRuleMatching(
    storeHash: string,
    checkoutData: CheckoutData,
    feeRule: FeeRuleData,
    customerGroupId?: number,
    channelId?: number
): Promise<{shouldApply: boolean, reason: string}> {

    // 1. Customer Group Validation ("Apply to All" when empty)
    const selectedCustomerGroups = feeRule.selectedCustomerGroups || [];
    if (selectedCustomerGroups.length > 0) {
        if (!customerGroupId) {
            return {
                shouldApply: false,
                reason: 'Customer data not available but fee requires specific customer groups'
            };
        }
        if (!selectedCustomerGroups.includes(customerGroupId)) {
            return {
                shouldApply: false,
                reason: `Customer group ${customerGroupId} not in selected groups [${selectedCustomerGroups.join(', ')}]`
            };
        }
        console.log(`Customer group ${customerGroupId} matches fee rule "${feeRule.name}" selected groups [${selectedCustomerGroups.join(', ')}]`);
    } else {
        console.log(`Customer group validation passed for fee "${feeRule.name}" - no customer group restrictions (apply to all customer groups)`);
    }

    // 2. Channel Validation ("Apply to All" when empty)
    const selectedChannels = feeRule.selectedChannels || [];
    if (selectedChannels.length > 0) {
        if (!channelId) {
            return {
                shouldApply: false,
                reason: 'Channel data not available but fee requires specific channels'
            };
        }
        if (!selectedChannels.includes(channelId)) {
            return {
                shouldApply: false,
                reason: `Channel ID ${channelId} not in selected channels [${selectedChannels.join(', ')}]`
            };
        }
        console.log(`Channel ID ${channelId} matches fee rule "${feeRule.name}" selected channels [${selectedChannels.join(', ')}]`);
    } else {
        console.log(`Channel validation passed for fee "${feeRule.name}" - no channel restrictions (apply to all channels)`);
    }

    // 3. Product Criteria Validation ("Apply to All" when empty, OR Logic when specified)
    const selectedProducts = feeRule.selectedProducts || [];
    const selectedBrands = feeRule.selectedBrands || [];
    const selectedCategories = feeRule.selectedCategories || [];

    if (selectedProducts.length > 0 || selectedBrands.length > 0 || selectedCategories.length > 0) {
        console.log(`Checking product criteria for fee "${feeRule.name}": products=[${selectedProducts.join(', ')}], brands=[${selectedBrands.join(', ')}], categories=[${selectedCategories.join(', ')}]`);

        const physicalItems = checkoutData?.cart?.line_items?.physical_items || [];
        let hasMatchingProduct = false;

        for (const item of physicalItems) {
            try {
                // Check direct product selection first
                if (selectedProducts.includes(item.product_id)) {
                    hasMatchingProduct = true;
                    console.log(`Product ${item.product_id} matches: directly selected`);
                    break;
                }

                // Check brand and category matching
                const accessToken = await getAccessToken(storeHash);
                const productData = await getProductDetails(storeHash, accessToken, item.product_id);

                const brandMatches = selectedBrands.length > 0 && selectedBrands.includes(productData.brand_id);
                const categoryMatches = selectedCategories.length > 0 &&
                    productData.categories.some(catId => selectedCategories.includes(catId));

                if (brandMatches || categoryMatches) {
                    hasMatchingProduct = true;
                    const matchReasons = [];
                    if (brandMatches) matchReasons.push(`brand_id=${productData.brand_id}`);
                    if (categoryMatches) matchReasons.push(`categories=[${productData.categories.join(', ')}]`);
                    console.log(`Product ${item.product_id} matches: ${matchReasons.join(' OR ')}`);
                    break;
                }
            } catch (error) {
                console.error(`Error checking product ${item.product_id} for fee "${feeRule.name}":`, error);
                // Continue checking other products
            }
        }

        if (!hasMatchingProduct) {
            return {
                shouldApply: false,
                reason: 'No products match the selected product/brand/category criteria'
            };
        }

        console.log(`Product criteria validation passed for fee "${feeRule.name}"`);
    } else {
        console.log(`Product criteria validation passed for fee "${feeRule.name}" - no product restrictions (apply to all products)`);
    }

    // All conditions matched or not specified, apply fee
    return {
        shouldApply: true,
        reason: 'All validation conditions passed'
    };
}

export interface FeeSynchronizationResult {
    success: boolean;
    action: 'update' | 'delete';
    checkoutId?: string;
    bigCommerceFeeId?: string;
    error?: string;
    details?: {
        feeRuleId: string;
        feeRuleName: string;
        checkoutFound: boolean;
        feeHistoryFound: boolean;
        bigCommerceOperationSuccess: boolean;
    };
}

/**
 * Find the most recent checkout with an applied fee for a given rule
 */
async function findRecentCheckoutWithAppliedFee(storeHash: string, feeRule: FeeRuleData) {
    try {
        // Get fee rule to access its application history
        const feeRuleData = await db.getFeeRule(storeHash, feeRule.id || '');
        const feeHistory = feeRuleData?.appliedFeeHistory || [];
        
        if (!feeHistory || feeHistory.length === 0) {
            console.log(`No fee application history found for rule "${feeRule.name}"`);
            return null;
        }
        
        // Sort by appliedAt timestamp in descending order to get the most recent
        const sortedHistory = [...feeHistory].sort((a, b) => b.appliedAt - a.appliedAt);
        const mostRecentFee = sortedHistory[0];
        
        console.log(`Found most recent fee application: checkout=${mostRecentFee.checkoutId}, feeId=${mostRecentFee.feeId}`);
        
        return {
            checkoutId: mostRecentFee.checkoutId,
            feeId: mostRecentFee.feeId
        };
    } catch (error) {
        console.error(`Error finding recent checkout with applied fee:`, error);
        return null;
    }
}

/**
 * Get access token for a store
 */
async function getAccessToken(storeHash: string): Promise<string> {
    const accessToken = await db.getStoreToken(storeHash);
    if (!accessToken) {
        throw new Error(`No access token found for store ${storeHash}`);
    }
    return accessToken;
}

/**
 * Validate that a checkout is still active and accessible
 */
async function validateCheckout(storeHash: string, checkoutId: string): Promise<boolean> {
    try {
        const accessToken = await getAccessToken(storeHash);
        await getCheckoutDetails(storeHash, accessToken, checkoutId);
        return true;
    } catch (error) {
        // 404 means checkout doesn't exist - this is expected for old checkouts
        if (error.status === 404) {
            console.log(`Checkout ${checkoutId} no longer exists (404) - this is normal for completed/expired checkouts`);
        } else {
            console.warn(`Checkout ${checkoutId} is no longer accessible:`, error);
        }
        return false;
    }
}

/**
 * Synchronize fee rule update with BigCommerce checkout
 */
export async function synchronizeFeeUpdate(
    storeHash: string,
    feeRule: FeeRuleData
): Promise<FeeSynchronizationResult> {
    const result: FeeSynchronizationResult = {
        success: false,
        action: 'update',
        details: {
            feeRuleId: feeRule.id || 'unknown',
            feeRuleName: feeRule.name,
            checkoutFound: false,
            feeHistoryFound: false,
            bigCommerceOperationSuccess: false
        }
    };

    try {
        console.log(`Starting fee synchronization for updated rule "${feeRule.name}"`);

        // Find recent checkout with applied fee
        const checkoutWithFee = await findRecentCheckoutWithAppliedFee(storeHash, feeRule);
        
        if (!checkoutWithFee) {
            result.error = 'No recent checkout found with applied fee for this rule';
            return result;
        }

        result.details!.feeHistoryFound = true;
        result.checkoutId = checkoutWithFee.checkoutId;
        result.bigCommerceFeeId = checkoutWithFee.feeId;

        // Validate checkout is still accessible
        const isCheckoutValid = await validateCheckout(storeHash, checkoutWithFee.checkoutId);
        
        if (!isCheckoutValid) {
            result.error = 'Checkout is no longer accessible';
            return result;
        }

        result.details!.checkoutFound = true;

        // Get checkout details for comprehensive validation and percentage calculation
        const accessToken = await getAccessToken(storeHash);
        const checkoutData = await getCheckoutDetails(storeHash, accessToken, checkoutWithFee.checkoutId);

        // Get customer and channel data for comprehensive validation
        let customerData: any;
        let channelData: any;
        try {
            const checkoutDbData = await db.getRecentCheckoutIds(storeHash, 1)
                .then(checkouts => checkouts.length > 0 ? checkouts[0] : null);
            if (checkoutDbData) {
                customerData = checkoutDbData.customer;
                channelData = checkoutDbData.channel;
                console.log(`Retrieved customer data for checkout ${checkoutWithFee.checkoutId}:`, customerData);
                console.log(`Retrieved channel data for checkout ${checkoutWithFee.checkoutId}:`, channelData);
            } else {
                console.log(`No customer/channel data found for checkout ${checkoutWithFee.checkoutId}`);
            }
        } catch (error) {
            console.warn(`Failed to retrieve customer/channel data for checkout ${checkoutWithFee.checkoutId}:`, error);
        }

        // Use comprehensive fee rule matching validation
        const customerGroupId = customerData?.customer_group_id;
        const channelId = extractChannelId(channelData);

        console.log(`Validating fee rule "${feeRule.name}" with customer group: ${customerGroupId}, channel: ${channelId}`);

        const validation = await validateFeeRuleMatching(
            storeHash,
            checkoutData,
            feeRule,
            customerGroupId,
            channelId
        );

        if (!validation.shouldApply) {
            console.log(`Removing fee "${feeRule.name}" from checkout ${checkoutWithFee.checkoutId} - ${validation.reason}`);

            // Delete the fee from BigCommerce
            await deleteCheckoutFee(
                storeHash,
                accessToken,
                checkoutWithFee.checkoutId,
                checkoutWithFee.feeId
            );

            result.details!.bigCommerceOperationSuccess = true;
            result.success = true;
            console.log(`Successfully removed fee "${feeRule.name}" due to validation failure: ${validation.reason}`);
            return result;
        }

        console.log(`Fee rule validation passed for "${feeRule.name}" - ${validation.reason}`);
        console.log(`Fee rule filters - selectedProducts: [${(feeRule.selectedProducts || []).join(', ')}], selectedBrands: [${(feeRule.selectedBrands || []).join(', ')}], selectedCategories: [${(feeRule.selectedCategories || []).join(', ')}]`);

        // Calculate the updated fee cost using filtered calculation (supports brand/category filters)
        const calculatedCost = await calculateFilteredFeeCost(storeHash, checkoutData, feeRule);

        console.log(`Calculated updated fee cost for "${feeRule.name}": ${calculatedCost}`);
        console.log(`Fee rule filters - selectedProducts: [${(feeRule.selectedProducts || []).join(', ')}], selectedBrands: [${(feeRule.selectedBrands || []).join(', ')}], selectedCategories: [${(feeRule.selectedCategories || []).join(', ')}]`);

        // If calculated cost is 0 or very small, remove the fee instead of updating it
        if (calculatedCost === 0 || calculatedCost < 0.01) {
            console.log(`Removing fee "${feeRule.name}" from checkout ${checkoutWithFee.checkoutId} - calculated cost is ${calculatedCost} (no products match updated filters)`);

            // Delete the fee from BigCommerce
            await deleteCheckoutFee(
                storeHash,
                accessToken,
                checkoutWithFee.checkoutId,
                checkoutWithFee.feeId
            );

            result.details!.bigCommerceOperationSuccess = true;
            result.success = true;
            console.log(`Successfully removed fee "${feeRule.name}" due to filter changes resulting in zero cost`);
            return result;
        }

        // Convert fee rule to BigCommerce format
        const feeRequest = convertFeeRuleToRequest(feeRule, calculatedCost);

        console.log(`Updating BigCommerce fee ${checkoutWithFee.feeId} in checkout ${checkoutWithFee.checkoutId} with new cost: ${calculatedCost}`);

        // Try to update the fee in BigCommerce, but handle the case where the fee was previously deleted
        try {
            // Attempt to update the existing fee
            const updatedFee = await updateCheckoutFee(
                storeHash,
                accessToken,
                checkoutWithFee.checkoutId,
                checkoutWithFee.feeId,
                feeRequest
            );
            
            result.details!.bigCommerceOperationSuccess = true;
            result.bigCommerceFeeId = updatedFee.id;

            // Update tracking in database
            if (feeRule.id) {
                await db.updateFeeRuleTracking(
                    storeHash,
                    feeRule.id,
                    updatedFee.id,
                    checkoutWithFee.checkoutId,
                    updatedFee.cost_inc_tax || updatedFee.cost_ex_tax || updatedFee.cost || calculatedCost
                );
            }
            
            result.success = true;
            console.log(`Successfully updated existing fee for rule "${feeRule.name}"`);
            
        } catch (updateError) {
            // Check if the error is because the fee no longer exists (was previously deleted)
            if (updateError.status === 422 && 
                (updateError.title?.includes('not available') || 
                 updateError.detail?.includes('not available'))) {
                
                console.log(`Fee ${checkoutWithFee.feeId} is no longer available (likely deleted). Creating a new fee instead.`);
                
                // Create a new fee instead of updating
                const newFee = await applyCheckoutFee(
                    storeHash,
                    accessToken,
                    checkoutWithFee.checkoutId,
                    feeRequest
                );
                
                result.details!.bigCommerceOperationSuccess = true;
                result.bigCommerceFeeId = newFee.id;
                
                // Update tracking in database with the new fee ID
                if (feeRule.id) {
                    await db.updateFeeRuleTracking(
                        storeHash,
                        feeRule.id,
                        newFee.id,
                        checkoutWithFee.checkoutId,
                        newFee.cost_inc_tax || newFee.cost_ex_tax || newFee.cost || calculatedCost
                    );
                }
                
                result.success = true;
                console.log(`Successfully created new fee for rule "${feeRule.name}" after previous fee was deleted`);
                
            } else {
                // Re-throw if it's not the specific error we're handling
                throw updateError;
            }
        }

    } catch (error) {
        // Handle 404 and 422 errors gracefully - fee or checkout no longer exists
        if (error.status === 404 || (error.status === 422 && error.title?.includes('not available'))) {
            console.log(`Fee or checkout no longer exists (${error.status}) for rule "${feeRule.name}" - considering sync successful`);
            result.success = true;
            result.error = `Fee or checkout no longer exists (${error.status}) - considered successful`;
        } else {
            console.error(`Error synchronizing fee update for rule "${feeRule.name}":`, error);
            result.error = error.message || 'Unknown synchronization error';
        }
    }

    return result;
}

/**
 * Synchronize fee rule deletion with BigCommerce checkout
 */
export async function synchronizeFeeDeletion(
    storeHash: string,
    feeRule: FeeRuleData
): Promise<FeeSynchronizationResult> {
    const result: FeeSynchronizationResult = {
        success: false,
        action: 'delete',
        details: {
            feeRuleId: feeRule.id || 'unknown',
            feeRuleName: feeRule.name,
            checkoutFound: false,
            feeHistoryFound: false,
            bigCommerceOperationSuccess: false
        }
    };

    try {
        console.log(`Starting fee synchronization for deleted rule "${feeRule.name}"`);

        // Find recent checkout with applied fee
        const checkoutWithFee = await findRecentCheckoutWithAppliedFee(storeHash, feeRule);
        
        if (!checkoutWithFee) {
            result.error = 'No recent checkout found with applied fee for this rule';
            return result;
        }

        result.details!.feeHistoryFound = true;
        result.checkoutId = checkoutWithFee.checkoutId;
        result.bigCommerceFeeId = checkoutWithFee.feeId;

        // Validate checkout is still accessible
        const isCheckoutValid = await validateCheckout(storeHash, checkoutWithFee.checkoutId);
        
        if (!isCheckoutValid) {
            result.error = 'Checkout is no longer accessible';
            return result;
        }

        result.details!.checkoutFound = true;

        console.log(`Deleting BigCommerce fee ${checkoutWithFee.feeId} from checkout ${checkoutWithFee.checkoutId}`);

        // Delete the fee from BigCommerce using the enhanced deletion method
        const accessToken = await getAccessToken(storeHash);
        await deleteCheckoutFee(
            storeHash,
            accessToken,
            checkoutWithFee.checkoutId,
            checkoutWithFee.feeId
        );

        console.log(`Fee deletion completed for rule "${feeRule.name}"`);

        // Verify the fee was actually deleted by checking the checkout
        try {
            const updatedCheckoutData = await getCheckoutDetails(storeHash, accessToken, checkoutWithFee.checkoutId);
            const remainingFees = updatedCheckoutData.fees || [];
            const deletedFeeStillExists = remainingFees.find(fee => fee.id === checkoutWithFee.feeId);

            if (deletedFeeStillExists) {
                console.warn(`Fee ${checkoutWithFee.feeId} still exists in checkout after deletion attempt`);
            } else {
                console.log(`Verified: Fee ${checkoutWithFee.feeId} successfully removed from checkout`);
            }
        } catch (verificationError) {
            // Don't fail the operation if verification fails
            console.warn(`Could not verify fee deletion:`, verificationError);
        }

        result.details!.bigCommerceOperationSuccess = true;
        result.success = true;
        console.log(`Successfully synchronized fee deletion for rule "${feeRule.name}"`);

    } catch (error) {
        // Handle 404 errors gracefully - fee or checkout no longer exists
        if (error.status === 404) {
            console.log(`Fee or checkout no longer exists (404) for rule "${feeRule.name}" - considering deletion successful`);
            result.success = true;
            result.error = 'Fee or checkout no longer exists (404) - considered successful';
        } else {
            console.error(`Error synchronizing fee deletion for rule "${feeRule.name}":`, error);
            result.error = error.message || 'Unknown synchronization error';
        }
    }

    return result;
}

/**
 * Synchronize fee rule activation with BigCommerce checkout
 * Applies the newly activated fee to the most recent checkout
 */
export async function synchronizeFeeActivation(
    storeHash: string,
    feeRule: FeeRuleData
): Promise<FeeSynchronizationResult> {
    const result: FeeSynchronizationResult = {
        success: false,
        action: 'update',
        details: {
            feeRuleId: feeRule.id || 'unknown',
            feeRuleName: feeRule.name,
            checkoutFound: false,
            feeHistoryFound: false,
            bigCommerceOperationSuccess: false
        }
    };

    try {
        console.log(`Starting fee activation synchronization for rule "${feeRule.name}"`);

        // Get the most recent checkout ID for this store
        const recentCheckouts = await db.getRecentCheckoutIds(storeHash, 1);

        if (recentCheckouts.length === 0) {
            result.error = 'No recent checkout found to apply the activated fee';
            return result;
        }

        const mostRecentCheckout = recentCheckouts[0];
        result.checkoutId = mostRecentCheckout.checkoutId;

        // Validate checkout is still accessible
        const isCheckoutValid = await validateCheckout(storeHash, mostRecentCheckout.checkoutId);

        if (!isCheckoutValid) {
            result.error = 'Most recent checkout is no longer accessible';
            return result;
        }

        result.details!.checkoutFound = true;

        // Get checkout details for comprehensive validation
        const accessToken = await getAccessToken(storeHash);
        const checkoutData = await getCheckoutDetails(storeHash, accessToken, mostRecentCheckout.checkoutId);

        // Get customer and channel data for comprehensive validation
        let customerData: any;
        let channelData: any;
        try {
            const checkoutDbData = await db.getRecentCheckoutIds(storeHash, 1)
                .then(checkouts => checkouts.length > 0 ? checkouts[0] : null);
            if (checkoutDbData) {
                customerData = checkoutDbData.customer;
                channelData = checkoutDbData.channel;
                console.log(`Retrieved customer data for checkout ${mostRecentCheckout.checkoutId}:`, customerData);
                console.log(`Retrieved channel data for checkout ${mostRecentCheckout.checkoutId}:`, channelData);
            } else {
                console.log(`No customer/channel data found for checkout ${mostRecentCheckout.checkoutId}`);
            }
        } catch (error) {
            console.warn(`Failed to retrieve customer/channel data for checkout ${mostRecentCheckout.checkoutId}:`, error);
        }

        // Use comprehensive fee rule matching validation
        const customerGroupId = customerData?.customer_group_id;
        const channelId = extractChannelId(channelData);

        console.log(`Validating fee rule "${feeRule.name}" for activation with customer group: ${customerGroupId}, channel: ${channelId}`);

        const validation = await validateFeeRuleMatching(
            storeHash,
            checkoutData,
            feeRule,
            customerGroupId,
            channelId
        );

        if (!validation.shouldApply) {
            console.log(`Skipping fee activation for "${feeRule.name}" - ${validation.reason}`);
            result.success = true; // Consider it successful since we're intentionally skipping
            result.error = `Fee activation skipped - ${validation.reason}`;
            return result;
        }

        console.log(`Fee rule validation passed for activation "${feeRule.name}" - ${validation.reason}`);

        console.log(`Applying activated fee "${feeRule.name}" to most recent checkout ${mostRecentCheckout.checkoutId}`);

        // Apply the specific fee rule to the most recent checkout using targeted approach
        const feeApplicationResult = await applyFeeRuleToMostRecentCheckout(storeHash, feeRule);

        if (feeApplicationResult.success) {
            if (feeApplicationResult.appliedFees.length > 0) {
                const appliedFee = feeApplicationResult.appliedFees[0];
                result.bigCommerceFeeId = appliedFee.id;
                result.details!.bigCommerceOperationSuccess = true;
                result.success = true;
                console.log(`Successfully applied activated fee "${feeRule.name}" with BigCommerce ID: ${appliedFee.id}`);
            } else {
                // Fee was not applied (likely due to filters not matching or fee already exists)
                console.log(`Fee "${feeRule.name}" was not applied - likely due to filters not matching or fee already exists`);
                result.success = true;
                result.error = 'Fee was not applied - likely due to filters not matching or fee already exists';
            }
        } else {
            const errorMessages = feeApplicationResult.errors.map(err => err.error).join('; ');
            result.error = `Fee application failed: ${errorMessages}`;
        }

    } catch (error) {
        // Handle 404 errors gracefully - checkout no longer exists
        if (error.status === 404) {
            console.log(`Checkout no longer exists (404) for fee activation "${feeRule.name}" - considering sync successful`);
            result.success = true;
            result.error = 'Checkout no longer exists (404) - considered successful';
        } else {
            console.error(`Error synchronizing fee activation for rule "${feeRule.name}":`, error);
            result.error = error.message || 'Unknown synchronization error';
        }
    }

    return result;
}

/**
 * Enhanced synchronization functions that delegate to the new bidirectional sync system
 * These functions maintain backward compatibility while leveraging the new webhook-based system
 */

/**
 * Enhanced fee update synchronization using bidirectional sync
 * This function now synchronizes across all active checkouts instead of just one
 */
export async function synchronizeFeeUpdateEnhanced(
    storeHash: string,
    feeRule: FeeRuleData
): Promise<any> {
    console.log(`Using enhanced synchronization for fee rule update: "${feeRule.name}"`);

    try {
        // Use the new bidirectional sync system
        const result = await synchronizeFeeRuleAcrossCheckouts(storeHash, feeRule, 'update');

        // Convert to legacy format for backward compatibility
        return {
            success: result.success,
            action: 'update',
            checkoutId: result.results?.[0]?.checkoutId,
            error: result.error,
            details: {
                feeRuleId: feeRule.id || 'unknown',
                feeRuleName: feeRule.name,
                processedCheckouts: result.processedCheckouts,
                successCount: result.successCount,
                errorCount: result.errorCount
            }
        };
    } catch (error) {
        console.error(`Error in enhanced fee update synchronization:`, error);
        return {
            success: false,
            action: 'update',
            error: error.message
        };
    }
}

/**
 * Enhanced fee deletion synchronization using bidirectional sync
 * This function now synchronizes across all active checkouts instead of just one
 */
export async function synchronizeFeeDeletionEnhanced(
    storeHash: string,
    feeRule: FeeRuleData
): Promise<any> {
    console.log(`Using enhanced synchronization for fee rule deletion: "${feeRule.name}"`);

    try {
        // Use the new bidirectional sync system
        const result = await synchronizeFeeRuleAcrossCheckouts(storeHash, feeRule, 'delete');

        // Convert to legacy format for backward compatibility
        return {
            success: result.success,
            action: 'delete',
            checkoutId: result.results?.[0]?.checkoutId,
            error: result.error,
            details: {
                feeRuleId: feeRule.id || 'unknown',
                feeRuleName: feeRule.name,
                processedCheckouts: result.processedCheckouts,
                successCount: result.successCount,
                errorCount: result.errorCount
            }
        };
    } catch (error) {
        console.error(`Error in enhanced fee deletion synchronization:`, error);
        return {
            success: false,
            action: 'delete',
            error: error.message
        };
    }
}
