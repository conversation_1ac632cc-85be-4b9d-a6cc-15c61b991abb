/**
 * Validation utilities for BigCommerce fee rules
 */

import db from './db';

/**
 * Check if a fee rule name is unique within a store
 * @param storeHash - BigCommerce store hash
 * @param name - Fee rule name to validate
 * @param excludeFeeRuleId - Optional fee rule ID to exclude from check (for edits)
 * @returns Promise<boolean> - true if name is unique, false if duplicate exists
 */
export async function isNameUnique(
    storeHash: string, 
    name: string, 
    excludeFeeRuleId?: string
): Promise<boolean> {
    if (!storeHash || !name) {
        return false;
    }

    try {
        // Get all fee rules for the store
        const feeRules = await db.getFeeRules(storeHash);
        
        // Check for duplicate names (case-insensitive)
        const trimmedName = name.trim().toLowerCase();
        const duplicateExists = feeRules.some(rule => {
            // Skip the current rule if we're editing
            if (excludeFeeRuleId && rule.id === excludeFeeRuleId) {
                return false;
            }
            
            // Compare names case-insensitively
            return rule.name.trim().toLowerCase() === trimmedName;
        });

        return !duplicateExists;
    } catch (error) {
        console.error('Error checking name uniqueness:', error);
        // In case of error, allow the name to prevent blocking user
        return true;
    }
}

/**
 * Validation result interface
 */
export interface NameValidationResult {
    isValid: boolean;
    error?: string;
}

/**
 * Validate fee rule name with detailed result
 * @param storeHash - BigCommerce store hash
 * @param name - Fee rule name to validate
 * @param excludeFeeRuleId - Optional fee rule ID to exclude from check (for edits)
 * @returns Promise<NameValidationResult> - Validation result with error message
 */
export async function validateFeeRuleName(
    storeHash: string, 
    name: string, 
    excludeFeeRuleId?: string
): Promise<NameValidationResult> {
    // Basic validation
    if (!name || !name.trim()) {
        return {
            isValid: false,
            error: 'Name is required'
        };
    }

    // Check uniqueness
    const isUnique = await isNameUnique(storeHash, name, excludeFeeRuleId);
    
    if (!isUnique) {
        return {
            isValid: false,
            error: 'Fee rule name must be unique. Please choose a different name.'
        };
    }

    return {
        isValid: true
    };
}
